{"data_mtime": 1752408380, "dep_lines": [5, 6, 7, 8, 9, 1, 1, 1, 1, 1, 14, 15, 21, 22], "dep_prios": [10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 5, 5, 5, 5], "dependencies": ["asyncio", "logging", "os", "sys", "datetime", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "d14f9d20f3c4c05d4818694e51d2eec8e7409ece", "id": "examples.workflow_example", "ignore_all": false, "interface_hash": "dd3e3923911ad3c8ce6a37139459bb913874fe1b", "mtime": 1752408378, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "examples\\workflow_example.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 8576, "suppressed": ["data_trans.workflow.config_manager", "data_trans.workflow.dag_templates", "data_trans.workflow.notifications", "data_trans.workflow.scheduler"], "version_id": "1.16.1"}