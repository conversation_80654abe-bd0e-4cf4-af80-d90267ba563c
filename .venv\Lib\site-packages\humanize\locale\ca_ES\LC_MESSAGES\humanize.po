# Catalan translations for PACKAGE package
# This file is distributed under the same license as the PACKAGE package.
# <PERSON><PERSON> Mas i Hernàndez <<EMAIL>>, 2021
#
msgid ""
msgstr ""
"Project-Id-Version: humanize\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2021-04-09 19:57+0200\n"
"Last-Translator: <PERSON><PERSON> Ma<PERSON> i Hernàndez <<EMAIL>>\n"
"Language-Team: Catalan\n"
"Language: ca\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n!=1;\n"
"X-Generator: Poedit 2.4.1\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "º"

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "º"

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "º"

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "º"

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "º"

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "º"

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "milió"
msgstr[1] "milió"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "mil milions"
msgstr[1] "mil milions"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "bilions"
msgstr[1] "bilions"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "quadrilió"
msgstr[1] "quadrilió"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "quintillió"
msgstr[1] "quintillió"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "sextilió"
msgstr[1] "sextilió"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "septilió"
msgstr[1] "septilió"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "octilió"
msgstr[1] "octilió"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "nonilió"
msgstr[1] "nonilió"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "decilió"
msgstr[1] "decilió"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "googol"
msgstr[1] "googol"

#: src/humanize/number.py:301
msgid "zero"
msgstr "zero"

#: src/humanize/number.py:302
msgid "one"
msgstr "un"

#: src/humanize/number.py:303
msgid "two"
msgstr "dos"

#: src/humanize/number.py:304
msgid "three"
msgstr "tres"

#: src/humanize/number.py:305
msgid "four"
msgstr "quatre"

#: src/humanize/number.py:306
msgid "five"
msgstr "cinc"

#: src/humanize/number.py:307
msgid "six"
msgstr "sis"

#: src/humanize/number.py:308
msgid "seven"
msgstr "set"

#: src/humanize/number.py:309
msgid "eight"
msgstr "vuit"

#: src/humanize/number.py:310
msgid "nine"
msgstr "nou"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d microsegon"
msgstr[1] "%d microsegons"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d mil·lisegons"
msgstr[1] "%d mil·lisegons"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "un moment"

#: src/humanize/time.py:167
msgid "a second"
msgstr "un segon"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d segon"
msgstr[1] "%d segons"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "un minut"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minut"
msgstr[1] "%d minuts"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "una hora"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d hora"
msgstr[1] "%d hores"

#: src/humanize/time.py:188
msgid "a day"
msgstr "un dia"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dia"
msgstr[1] "%d dies"

#: src/humanize/time.py:197
msgid "a month"
msgstr "un mes"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d mes"
msgstr[1] "%d mesos"

#: src/humanize/time.py:203
msgid "a year"
msgstr "un any"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 any, %d dia"
msgstr[1] "1 any, %d dies"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1 any, 1 mes"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 any, %d mes"
msgstr[1] "1 any, %d mesos"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d any"
msgstr[1] "%d anys"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "en %s"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "fa %s"

#: src/humanize/time.py:260
msgid "now"
msgstr "ara"

#: src/humanize/time.py:284
msgid "today"
msgstr "avui"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "demà"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "ahir"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s i %s"
