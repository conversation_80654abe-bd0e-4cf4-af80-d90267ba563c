{".class": "MypyFile", "_fullname": "examples.workflow_example", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.workflow_example.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.workflow_example.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.workflow_example.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.workflow_example.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.workflow_example.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.workflow_example.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "create_batch_process_dag": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "examples.workflow_example.create_batch_process_dag", "name": "create_batch_process_dag", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "examples.workflow_example.create_batch_process_dag", "source_any": null, "type_of_any": 3}}}, "create_realtime_dag": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "examples.workflow_example.create_realtime_dag", "name": "create_realtime_dag", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "examples.workflow_example.create_realtime_dag", "source_any": null, "type_of_any": 3}}}, "create_scheduled_dag": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "examples.workflow_example.create_scheduled_dag", "name": "create_scheduled_dag", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "examples.workflow_example.create_scheduled_dag", "source_any": null, "type_of_any": 3}}}, "create_simple_crawl_dag": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "examples.workflow_example.create_simple_crawl_dag", "name": "create_simple_crawl_dag", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "examples.workflow_example.create_simple_crawl_dag", "source_any": null, "type_of_any": 3}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "demo_batch_process_dag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.workflow_example.demo_batch_process_dag", "name": "demo_batch_process_dag", "type": null}}, "demo_configuration_management": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.workflow_example.demo_configuration_management", "name": "demo_configuration_management", "type": null}}, "demo_realtime_dag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.workflow_example.demo_realtime_dag", "name": "demo_realtime_dag", "type": null}}, "demo_scheduled_dag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.workflow_example.demo_scheduled_dag", "name": "demo_scheduled_dag", "type": null}}, "demo_scheduler_management": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.workflow_example.demo_scheduler_management", "name": "demo_scheduler_management", "type": null}}, "demo_simple_crawl_dag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.workflow_example.demo_simple_crawl_dag", "name": "demo_simple_crawl_dag", "type": null}}, "get_config_manager": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "examples.workflow_example.get_config_manager", "name": "get_config_manager", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "examples.workflow_example.get_config_manager", "source_any": null, "type_of_any": 3}}}, "get_notification_manager": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "examples.workflow_example.get_notification_manager", "name": "get_notification_manager", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "examples.workflow_example.get_notification_manager", "source_any": null, "type_of_any": 3}}}, "get_scheduler": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "examples.workflow_example.get_scheduler", "name": "get_scheduler", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "examples.workflow_example.get_scheduler", "source_any": null, "type_of_any": 3}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "examples.workflow_example.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.workflow_example.main", "name": "main", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "setup_workflow_environment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.workflow_example.setup_workflow_environment", "name": "setup_workflow_environment", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "examples\\workflow_example.py"}