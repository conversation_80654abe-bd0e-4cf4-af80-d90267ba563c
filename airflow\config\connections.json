{"connections": [{"conn_id": "postgres_default", "conn_type": "postgres", "host": "localhost", "port": 5432, "schema": "datatrans", "login": "postgres", "password": "postgres", "extra": null, "description": "默认PostgreSQL连接", "created_at": "2025-07-13T16:17:13.385042", "updated_at": "2025-07-13T16:17:13.385042"}, {"conn_id": "redis_default", "conn_type": "redis", "host": "localhost", "port": 6379, "schema": "0", "login": null, "password": "", "extra": null, "description": "默认Redis连接", "created_at": "2025-07-13T16:17:13.385042", "updated_at": "2025-07-13T16:17:13.385042"}, {"conn_id": "mongodb_default", "conn_type": "mongodb", "host": "localhost", "port": 27017, "schema": "datatrans", "login": "", "password": "", "extra": null, "description": "默认MongoDB连接", "created_at": "2025-07-13T16:17:13.385042", "updated_at": "2025-07-13T16:17:13.385042"}, {"conn_id": "http_default", "conn_type": "http", "host": "httpbin.org", "port": 443, "schema": null, "login": null, "password": null, "extra": null, "description": "默认HTTP连接", "created_at": "2025-07-13T16:17:13.385042", "updated_at": "2025-07-13T16:17:13.385042"}, {"conn_id": "test_connection", "conn_type": "http", "host": "example.com", "port": 80, "schema": null, "login": null, "password": null, "extra": null, "description": "测试连接", "created_at": "2025-07-13T16:17:13.386913", "updated_at": "2025-07-13T16:17:13.386913"}], "updated_at": "2025-07-13T16:17:13.386913"}