Metadata-Version: 2.3
Name: apache-airflow-providers-celery
Version: 3.10.0
Summary: Provider package apache-airflow-providers-celery for Apache Airflow
Keywords: airflow-provider,celery,airflow,integration
Author-email: Apache Software Foundation <<EMAIL>>
Maintainer-email: Apache Software Foundation <<EMAIL>>
Requires-Python: ~=3.9
Description-Content-Type: text/x-rst
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: Framework :: Apache Airflow
Classifier: Framework :: Apache Airflow :: Provider
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: System :: Monitoring
Requires-Dist: apache-airflow>=2.9.0
Requires-Dist: celery[redis]>=5.3.0,<6,!=5.3.3,!=5.3.2
Requires-Dist: flower>=1.0.0
Requires-Dist: google-re2>=1.0
Requires-Dist: apache-airflow-providers-cncf-kubernetes>=7.4.0 ; extra == "cncf-kubernetes"
Project-URL: Bug Tracker, https://github.com/apache/airflow/issues
Project-URL: Changelog, https://airflow.apache.org/docs/apache-airflow-providers-celery/3.10.0/changelog.html
Project-URL: Documentation, https://airflow.apache.org/docs/apache-airflow-providers-celery/3.10.0
Project-URL: Slack Chat, https://s.apache.org/airflow-slack
Project-URL: Source Code, https://github.com/apache/airflow
Project-URL: Twitter, https://x.com/ApacheAirflow
Project-URL: YouTube, https://www.youtube.com/channel/UCSXwxpWZQ7XZ1WL3wqevChA/
Provides-Extra: cncf-kubernetes


 .. Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

 ..   http://www.apache.org/licenses/LICENSE-2.0

 .. Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

 .. NOTE! THIS FILE IS AUTOMATICALLY GENERATED AND WILL BE OVERWRITTEN!

 .. IF YOU WANT TO MODIFY TEMPLATE FOR THIS FILE, YOU SHOULD MODIFY THE TEMPLATE
    `PROVIDER_README_TEMPLATE.rst.jinja2` IN the `dev/breeze/src/airflow_breeze/templates` DIRECTORY


Package ``apache-airflow-providers-celery``

Release: ``3.10.0``


`Celery <https://docs.celeryq.dev/en/stable/>`__


Provider package
----------------

This is a provider package for ``celery`` provider. All classes for this provider package
are in ``airflow.providers.celery`` python package.

You can find package information and changelog for the provider
in the `documentation <https://airflow.apache.org/docs/apache-airflow-providers-celery/3.10.0/>`_.

Installation
------------

You can install this package on top of an existing Airflow 2 installation (see ``Requirements`` below
for the minimum Airflow version supported) via
``pip install apache-airflow-providers-celery``

The package supports the following python versions: 3.9,3.10,3.11,3.12

Requirements
------------

==================  ==============================
PIP package         Version required
==================  ==============================
``apache-airflow``  ``>=2.9.0``
``celery[redis]``   ``>=5.3.0,!=5.3.2,!=5.3.3,<6``
``flower``          ``>=1.0.0``
``google-re2``      ``>=1.0``
==================  ==============================

Cross provider package dependencies
-----------------------------------

Those are dependencies that might be needed in order to use all the features of the package.
You need to install the specified provider packages in order to use them.

You can install such cross-provider dependencies when installing from PyPI. For example:

.. code-block:: bash

    pip install apache-airflow-providers-celery[cncf.kubernetes]


======================================================================================================================  ===================
Dependent package                                                                                                       Extra
======================================================================================================================  ===================
`apache-airflow-providers-cncf-kubernetes <https://airflow.apache.org/docs/apache-airflow-providers-cncf-kubernetes>`_  ``cncf.kubernetes``
======================================================================================================================  ===================

The changelog for the provider package can be found in the
`changelog <https://airflow.apache.org/docs/apache-airflow-providers-celery/3.10.0/changelog.html>`_.

