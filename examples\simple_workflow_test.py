"""
简化的工作流测试 - 避免Ray依赖
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)


async def test_workflow_base():
    """测试工作流基础功能"""
    logger.info("=== 测试工作流基础功能 ===")

    from data_trans.workflow.base import BaseDAG, BaseOperator, TaskState, XCom

    # 创建测试操作器
    class TestOperator(BaseOperator):
        def __init__(self, task_id: str, dag, return_value=None, **kwargs):
            super().__init__(task_id, dag, **kwargs)
            self.return_value = return_value
            self.executed = False

        async def execute(self, context):
            self.executed = True
            logger.info(f"执行任务: {self.task_id}")
            return self.return_value

    # 创建DAG
    dag = BaseDAG(dag_id="test_dag", description="测试DAG", start_date=datetime.now())

    # 创建任务
    task1 = TestOperator("task1", dag, return_value="result1")
    task2 = TestOperator("task2", dag, return_value="result2")
    task3 = TestOperator("task3", dag, return_value="result3")

    # 设置依赖关系
    task1 >> task2 >> task3

    # 验证DAG
    assert dag.validate() == True
    logger.info("✓ DAG验证通过")

    # 执行DAG
    success = await dag.run()
    assert success == True
    logger.info("✓ DAG执行成功")

    # 验证任务执行
    assert task1.executed == True
    assert task2.executed == True
    assert task3.executed == True
    logger.info("✓ 所有任务执行完成")

    return True


async def test_workflow_sensors():
    """测试工作流传感器"""
    logger.info("=== 测试工作流传感器 ===")

    from data_trans.workflow.base import BaseDAG
    from data_trans.workflow.sensors import TimeSensor

    dag = BaseDAG(dag_id="sensor_test_dag")

    # 测试时间传感器（过去的时间应该立即触发）
    past_time = (datetime.now().replace(second=0, microsecond=0)).strftime("%H:%M")

    time_sensor = TimeSensor(
        task_id="test_time_sensor", dag=dag, target_time=past_time, check_interval=1
    )

    # 创建模拟上下文
    class MockTaskInstance:
        def log_info(self, message):
            logger.info(f"[传感器] {message}")

    context = {
        "dag": dag,
        "task": time_sensor,
        "task_instance": MockTaskInstance(),
        "execution_date": datetime.now(),
    }

    # 测试传感器
    result = await time_sensor.poke(context)
    assert result == True
    logger.info("✓ 时间传感器测试通过")

    return True


async def test_workflow_notifications():
    """测试工作流通知"""
    logger.info("=== 测试工作流通知 ===")

    from data_trans.workflow.notifications import LogNotifier, NotificationManager

    # 创建通知管理器
    config = {"log": {"enabled": True}}

    manager = NotificationManager(config)

    # 发送测试通知
    results = await manager.send_notification(
        message="这是一条测试消息", subject="工作流测试通知", channels=["log"]
    )

    assert "log" in results
    assert results["log"] == True
    logger.info("✓ 通知系统测试通过")

    # 测试成功通知
    await manager.send_success_notification(
        dag_id="test_dag", execution_date=datetime.now().isoformat()
    )
    logger.info("✓ 成功通知测试通过")

    # 测试失败通知
    await manager.send_failure_notification(
        dag_id="test_dag",
        execution_date=datetime.now().isoformat(),
        error="测试错误信息",
    )
    logger.info("✓ 失败通知测试通过")

    return True


async def test_workflow_scheduler():
    """测试工作流调度器"""
    logger.info("=== 测试工作流调度器 ===")

    from data_trans.workflow.base import BaseDAG, BaseOperator
    from data_trans.workflow.scheduler import DAGRunState, WorkflowScheduler

    # 创建测试操作器
    class SimpleOperator(BaseOperator):
        async def execute(self, context):
            logger.info(f"执行任务: {self.task_id}")
            return f"result_{self.task_id}"

    # 创建调度器
    scheduler = WorkflowScheduler()

    # 创建测试DAG
    dag = BaseDAG(dag_id="scheduler_test_dag")
    task = SimpleOperator("test_task", dag)

    # 注册DAG
    scheduler.register_dag(dag)
    assert "scheduler_test_dag" in scheduler.dags
    logger.info("✓ DAG注册成功")

    # 触发DAG执行
    run_id = await scheduler.trigger_dag("scheduler_test_dag")
    assert run_id is not None
    logger.info(f"✓ DAG触发成功，运行ID: {run_id}")

    # 等待执行完成
    await asyncio.sleep(1)

    # 检查执行结果
    dag_run = scheduler.get_dag_run(run_id)
    assert dag_run is not None
    logger.info(f"✓ DAG执行状态: {dag_run.state}")

    # 获取调度器状态
    status = scheduler.get_scheduler_status()
    assert "running" in status
    logger.info(f"✓ 调度器状态: {status}")

    return True


async def test_workflow_config():
    """测试工作流配置管理"""
    logger.info("=== 测试工作流配置管理 ===")

    from data_trans.workflow.config_manager import ConfigManager
    from data_trans.workflow.connections import Connection

    # 创建配置管理器
    config_manager = ConfigManager()

    # 添加测试连接
    success = config_manager.add_connection(
        conn_id="test_connection",
        conn_type="http",
        host="example.com",
        port=80,
        description="测试连接",
    )
    assert success == True
    logger.info("✓ 连接添加成功")

    # 获取连接
    conn = config_manager.get_connection("test_connection")
    assert conn is not None
    assert conn.conn_id == "test_connection"
    logger.info("✓ 连接获取成功")

    # 设置测试变量
    success = config_manager.set_variable("test_variable", {"key": "value"}, "测试变量")
    assert success == True
    logger.info("✓ 变量设置成功")

    # 获取变量
    value = config_manager.get_variable("test_variable")
    assert value is not None
    assert value["key"] == "value"
    logger.info("✓ 变量获取成功")

    # 获取配置摘要
    summary = config_manager.get_config_summary()
    assert "connections" in summary
    assert "variables" in summary
    logger.info(f"✓ 配置摘要: {summary}")

    return True


async def test_dag_templates():
    """测试DAG模板"""
    logger.info("=== 测试DAG模板 ===")

    from data_trans.workflow.dag_templates import create_simple_crawl_dag

    # 创建简单爬取DAG
    dag = create_simple_crawl_dag(
        dag_id="template_test_dag",
        target_urls=["http://example.com"],
        crawler_config={"timeout": 30},
        cleaner_config={"remove_html": True},
        storage_config={"type": "mongodb"},
    )

    assert dag.dag_id == "template_test_dag"
    assert len(dag.tasks) == 4  # crawler, cleaner, storage, notification
    logger.info("✓ 简单爬取DAG模板创建成功")

    # 验证任务依赖关系
    crawler_task = dag.get_task("crawler")
    cleaner_task = dag.get_task("cleaner")
    storage_task = dag.get_task("storage")
    notification_task = dag.get_task("notification")

    assert "cleaner" in crawler_task.downstream_task_ids
    assert "storage" in cleaner_task.downstream_task_ids
    assert "notification" in storage_task.downstream_task_ids
    logger.info("✓ 任务依赖关系验证通过")

    return True


async def main():
    """主测试函数"""
    logger.info("开始工作流系统测试...")

    try:
        # 运行各项测试
        await test_workflow_base()
        await test_workflow_sensors()
        await test_workflow_notifications()
        await test_workflow_scheduler()
        await test_workflow_config()
        await test_dag_templates()

        logger.info("🎉 所有工作流测试通过！")

        # 演示完整工作流
        logger.info("=== 演示完整工作流 ===")

        from data_trans.workflow.dag_templates import create_simple_crawl_dag
        from data_trans.workflow.scheduler import WorkflowScheduler

        # 创建演示DAG
        demo_dag = create_simple_crawl_dag(
            dag_id="demo_workflow",
            target_urls=["https://httpbin.org/json"],
            crawler_config={"timeout": 30},
            cleaner_config={"remove_html": True},
            storage_config={"type": "mongodb", "collection": "demo_data"},
        )

        # 创建调度器并执行
        scheduler = WorkflowScheduler()
        scheduler.register_dag(demo_dag)

        run_id = await scheduler.trigger_dag("demo_workflow")
        logger.info(f"演示工作流已启动，运行ID: {run_id}")

        # 等待执行完成
        await asyncio.sleep(3)

        dag_run = scheduler.get_dag_run(run_id)
        if dag_run:
            logger.info(f"演示工作流执行状态: {dag_run.state}")
            logger.info("最近的日志消息:")
            for msg in dag_run.log_messages[-5:]:
                logger.info(f"  {msg}")

        logger.info("✅ 工作流系统测试和演示完成！")

    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback

        traceback.print_exc()
        raise


if __name__ == "__main__":
    asyncio.run(main())
