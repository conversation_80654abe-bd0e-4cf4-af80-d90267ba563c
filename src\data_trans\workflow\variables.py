"""
变量管理 - 模拟Airflow的变量管理功能
"""

import json
import logging
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class Variable:
    """变量配置"""

    key: str
    value: Any
    description: Optional[str] = None
    is_encrypted: bool = False
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Variable":
        """从字典创建变量"""
        return cls(**data)


class VariableManager:
    """变量管理器"""

    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "airflow/config/variables.json"
        self.variables: Dict[str, Variable] = {}
        self._load_variables()

    def _load_variables(self):
        """加载变量配置"""
        config_path = Path(self.config_file)
        if config_path.exists():
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    for var_data in data.get("variables", []):
                        var = Variable.from_dict(var_data)
                        self.variables[var.key] = var
                logger.info(f"加载了 {len(self.variables)} 个变量配置")
            except Exception as e:
                logger.error(f"加载变量配置失败: {e}")
        else:
            logger.info("变量配置文件不存在，将创建默认配置")
            self._create_default_variables()

    def _save_variables(self):
        """保存变量配置"""
        config_path = Path(self.config_file)
        config_path.parent.mkdir(parents=True, exist_ok=True)

        data = {
            "variables": [var.to_dict() for var in self.variables.values()],
            "updated_at": datetime.now().isoformat(),
        }

        try:
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"保存变量配置到: {config_path}")
        except Exception as e:
            logger.error(f"保存变量配置失败: {e}")

    def _create_default_variables(self):
        """创建默认变量"""
        default_variables = [
            Variable(
                key="crawler_config",
                value={
                    "timeout": 30,
                    "max_retries": 3,
                    "delay": 1,
                    "user_agent": "DataTrans/1.0",
                },
                description="默认爬虫配置",
            ),
            Variable(
                key="storage_config",
                value={
                    "type": "mongodb",
                    "database": "datatrans",
                    "collection": "crawl_data",
                },
                description="默认存储配置",
            ),
            Variable(
                key="notification_config",
                value={
                    "channels": ["log", "email"],
                    "email_recipients": ["<EMAIL>"],
                    "slack_webhook": "",
                },
                description="默认通知配置",
            ),
            Variable(
                key="system_settings",
                value={
                    "max_concurrent_tasks": 10,
                    "task_timeout": 3600,
                    "retry_delay": 300,
                },
                description="系统设置",
            ),
            Variable(
                key="api_keys",
                value={"openai_api_key": "", "google_api_key": "", "github_token": ""},
                description="API密钥配置",
                is_encrypted=True,
            ),
        ]

        for var in default_variables:
            self.variables[var.key] = var

        self._save_variables()

    def set_variable(
        self,
        key: str,
        value: Any,
        description: Optional[str] = None,
        is_encrypted: bool = False,
    ) -> bool:
        """设置变量"""
        try:
            if key in self.variables:
                # 更新现有变量
                var = self.variables[key]
                var.value = value
                var.updated_at = datetime.now().isoformat()
                if description is not None:
                    var.description = description
                var.is_encrypted = is_encrypted
            else:
                # 创建新变量
                var = Variable(
                    key=key,
                    value=value,
                    description=description,
                    is_encrypted=is_encrypted,
                )
                self.variables[key] = var

            self._save_variables()
            logger.info(f"设置变量: {key}")
            return True
        except Exception as e:
            logger.error(f"设置变量失败: {key}, 错误: {e}")
            return False

    def get_variable(self, key: str, default_value: Any = None) -> Any:
        """获取变量值"""
        var = self.variables.get(key)
        if var is None:
            if default_value is not None:
                logger.warning(f"变量不存在，使用默认值: {key}")
                return default_value
            else:
                logger.error(f"变量不存在: {key}")
                return None

        return var.value

    def get_variable_info(self, key: str) -> Optional[Variable]:
        """获取变量信息"""
        return self.variables.get(key)

    def delete_variable(self, key: str) -> bool:
        """删除变量"""
        if key not in self.variables:
            logger.error(f"变量不存在: {key}")
            return False

        try:
            del self.variables[key]
            self._save_variables()
            logger.info(f"删除变量: {key}")
            return True
        except Exception as e:
            logger.error(f"删除变量失败: {key}, 错误: {e}")
            return False

    def list_variables(self) -> List[Variable]:
        """列出所有变量"""
        return list(self.variables.values())

    def update_variable(self, key: str, **kwargs) -> bool:
        """更新变量属性"""
        if key not in self.variables:
            logger.error(f"变量不存在: {key}")
            return False

        try:
            var = self.variables[key]
            for attr, value in kwargs.items():
                if hasattr(var, attr):
                    setattr(var, attr, value)
            var.updated_at = datetime.now().isoformat()

            self._save_variables()
            logger.info(f"更新变量: {key}")
            return True
        except Exception as e:
            logger.error(f"更新变量失败: {key}, 错误: {e}")
            return False

    def export_variables(self, file_path: str) -> bool:
        """导出变量到文件"""
        try:
            data = {
                "variables": [var.to_dict() for var in self.variables.values()],
                "exported_at": datetime.now().isoformat(),
            }

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            logger.info(f"导出变量到: {file_path}")
            return True
        except Exception as e:
            logger.error(f"导出变量失败: {e}")
            return False

    def import_variables(self, file_path: str, overwrite: bool = False) -> bool:
        """从文件导入变量"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            imported_count = 0
            for var_data in data.get("variables", []):
                var = Variable.from_dict(var_data)

                if var.key in self.variables and not overwrite:
                    logger.warning(f"变量已存在，跳过: {var.key}")
                    continue

                self.variables[var.key] = var
                imported_count += 1

            if imported_count > 0:
                self._save_variables()

            logger.info(f"导入了 {imported_count} 个变量")
            return True
        except Exception as e:
            logger.error(f"导入变量失败: {e}")
            return False


# 全局变量管理器实例
_variable_manager: Optional[VariableManager] = None


def get_variable_manager() -> VariableManager:
    """获取变量管理器实例"""
    global _variable_manager
    if _variable_manager is None:
        _variable_manager = VariableManager()
    return _variable_manager


def get_variable(key: str, default_value: Any = None) -> Any:
    """获取变量（便捷函数）"""
    return get_variable_manager().get_variable(key, default_value)


def set_variable(
    key: str, value: Any, description: Optional[str] = None, is_encrypted: bool = False
) -> bool:
    """设置变量（便捷函数）"""
    return get_variable_manager().set_variable(key, value, description, is_encrypted)
