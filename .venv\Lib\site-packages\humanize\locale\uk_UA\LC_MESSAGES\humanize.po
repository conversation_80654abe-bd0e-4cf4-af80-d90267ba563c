msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: \n"
"Last-Translator: TL\n"
"Language-Team: uk_UA\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"Generated-By:\n"
"X-Generator: \n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "ий"

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "ий"

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "ій"

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "ий"

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "ий"

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "ій"

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "ий"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "тисяча"
msgstr[1] "тисячі"
msgstr[2] "тисяч"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "мільйон"
msgstr[1] "мільйона"
msgstr[2] "мільйонів"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "мільярд"
msgstr[1] "мільярда"
msgstr[2] "мільярдів"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "трильйон"
msgstr[1] "трильйона"
msgstr[2] "трильйонів"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "квадрильйон"
msgstr[1] "квадрильйона"
msgstr[2] "квадрильйонів"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "квинтиліон"
msgstr[1] "квинтиліона"
msgstr[2] "квинтиліонів"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "сикстильйон"
msgstr[1] "сикстильйона"
msgstr[2] "сикстильйонів"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "септильйон"
msgstr[1] "септильйона"
msgstr[2] "септильйонів"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "октильйон"
msgstr[1] "октильйона"
msgstr[2] "октильйонів"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "нонильйон"
msgstr[1] "нонильйона"
msgstr[2] "нонильйонів"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "децильйон"
msgstr[1] "децильйона"
msgstr[2] "децильйонів"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "гугол"
msgstr[1] "гугла"
msgstr[2] "гуглів"

#: src/humanize/number.py:301
msgid "zero"
msgstr "нуль"

#: src/humanize/number.py:302
msgid "one"
msgstr "один"

#: src/humanize/number.py:303
msgid "two"
msgstr "два"

#: src/humanize/number.py:304
msgid "three"
msgstr "три"

#: src/humanize/number.py:305
msgid "four"
msgstr "чотири"

#: src/humanize/number.py:306
msgid "five"
msgstr "п'ять"

#: src/humanize/number.py:307
msgid "six"
msgstr "шість"

#: src/humanize/number.py:308
msgid "seven"
msgstr "сім"

#: src/humanize/number.py:309
msgid "eight"
msgstr "вісім"

#: src/humanize/number.py:310
msgid "nine"
msgstr "дев'ять"

#: src/humanize/time.py:152
#, fuzzy, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d мікросекунда"
msgstr[1] "%d мікросекунди"
msgstr[2] "%d мікросекунд"

#: src/humanize/time.py:161
#, fuzzy, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d мілісекунда"
msgstr[1] "%d мілісекунди"
msgstr[2] "%d мілісекунд"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "у цей момент"

#: src/humanize/time.py:167
msgid "a second"
msgstr "секунда"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d секунда"
msgstr[1] "%d секунди"
msgstr[2] "%d секунд"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "хвилина"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d хвилина"
msgstr[1] "%d хвилини"
msgstr[2] "%d хвилин"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "година"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d година"
msgstr[1] "%d години"
msgstr[2] "%d годин"

#: src/humanize/time.py:188
msgid "a day"
msgstr "день"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d день"
msgstr[1] "%d дня"
msgstr[2] "%d днів"

#: src/humanize/time.py:197
msgid "a month"
msgstr "місяць"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d місяць"
msgstr[1] "%d місяця"
msgstr[2] "%d місяців"

#: src/humanize/time.py:203
msgid "a year"
msgstr "рік"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 рік, %d день"
msgstr[1] "1 рік, %d дня"
msgstr[2] "1 рік, %d днів"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1 рік, 1 місяць"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 рік, %d місяць"
msgstr[1] "1 рік, %d місяця"
msgstr[2] "1 рік, %d місяців"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d рік"
msgstr[1] "%d роки"
msgstr[2] "%d років"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "через %s"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s назад"

#: src/humanize/time.py:260
msgid "now"
msgstr "зараз"

#: src/humanize/time.py:284
msgid "today"
msgstr "сьогодні"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "завтра"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "вчора"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s й %s"
