# Italian translations for PACKAGE package.
# Copyright (C) 2018 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2018.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2018-10-27 22:52+0200\n"
"Last-Translator: derfel <<EMAIL>>\n"
"Language-Team: Italian\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.2\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "º"

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "º"

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "º"

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "º"

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "ª"

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "ª"

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "ª"

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "ª"

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "ª"

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "ª"

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "ª"

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "ª"

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "ª"

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "ª"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "migliaio"
msgstr[1] "migliaia"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "milione"
msgstr[1] "milioni"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "miliardo"
msgstr[1] "miliardi"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "bilione"
msgstr[1] "bilioni"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "biliardo"
msgstr[1] "biliardi"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "trilione"
msgstr[1] "trilioni"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "triliardo"
msgstr[1] "triliardi"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "quadrilione"
msgstr[1] "quadrilioni"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "quadriliardo"
msgstr[1] "quadriliardi"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "quintilione"
msgstr[1] "quintilioni"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "quintiliardo"
msgstr[1] "quintiliardi"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "googol"
msgstr[1] "googol"

#: src/humanize/number.py:301
msgid "zero"
msgstr "zero"

#: src/humanize/number.py:302
msgid "one"
msgstr "uno"

#: src/humanize/number.py:303
msgid "two"
msgstr "due"

#: src/humanize/number.py:304
msgid "three"
msgstr "tre"

#: src/humanize/number.py:305
msgid "four"
msgstr "quattro"

#: src/humanize/number.py:306
msgid "five"
msgstr "cinque"

#: src/humanize/number.py:307
msgid "six"
msgstr "sei"

#: src/humanize/number.py:308
msgid "seven"
msgstr "sette"

#: src/humanize/number.py:309
msgid "eight"
msgstr "otto"

#: src/humanize/number.py:310
msgid "nine"
msgstr "nove"

#: src/humanize/time.py:152
#, fuzzy, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d microsecondo"
msgstr[1] "%d microsecondi"

#: src/humanize/time.py:161
#, fuzzy, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d millisecondo"
msgstr[1] "%d millisecondi"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "un momento"

#: src/humanize/time.py:167
msgid "a second"
msgstr "un secondo"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d secondo"
msgstr[1] "%d secondi"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "un minuto"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minuto"
msgstr[1] "%d minuti"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "un'ora"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d ora"
msgstr[1] "%d ore"

#: src/humanize/time.py:188
msgid "a day"
msgstr "un giorno"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d giorno"
msgstr[1] "%d giorni"

#: src/humanize/time.py:197
msgid "a month"
msgstr "un mese"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d mese"
msgstr[1] "%d mesi"

#: src/humanize/time.py:203
msgid "a year"
msgstr "un anno"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "un anno e %d giorno"
msgstr[1] "un anno e %d giorni"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "un anno ed un mese"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "un anno e %d mese"
msgstr[1] "un anno e %d mesi"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d anno"
msgstr[1] "%d anni"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "fra %s"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s fa"

#: src/humanize/time.py:260
msgid "now"
msgstr "adesso"

#: src/humanize/time.py:284
msgid "today"
msgstr "oggi"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "domani"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "ieri"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s e %s"
