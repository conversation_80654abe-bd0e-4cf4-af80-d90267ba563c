# Slovenian translations for PACKAGE package.
# Copyright (C) 2021 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: humanize\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2021-06-11 23:39+0200\n"
"Last-Translator: dkrat7 <dkrat7 @github.com>\n"
"Language-Team: Slovenian\n"
"Language: sl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || "
"n%100==4 ? 2 : 3);\n"
"X-Generator: Poedit 2.3\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "."

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "."

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "tisoč"
msgstr[1] "tisoč"
msgstr[2] "tisoč"
msgstr[3] "tisoč"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "milijon"
msgstr[1] "milijona"
msgstr[2] "milijoni"
msgstr[3] "milijonov"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "milijarda"
msgstr[1] "milijardi"
msgstr[2] "milijarde"
msgstr[3] "milijard"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "bilijon"
msgstr[1] "bilijona"
msgstr[2] "bilijoni"
msgstr[3] "bilijonov"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "bilijarda"
msgstr[1] "bilijardi"
msgstr[2] "bilijarde"
msgstr[3] "bilijard"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "trilijon"
msgstr[1] "trilijona"
msgstr[2] "trilijoni"
msgstr[3] "trilijonov"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "trilijarda"
msgstr[1] "trilijardi"
msgstr[2] "trilijarde"
msgstr[3] "trilijard"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "kvadrilijon"
msgstr[1] "kvadrilijona"
msgstr[2] "kvadrilijoni"
msgstr[3] "kvadrilijonov"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "kvadrilijarda"
msgstr[1] "kvadrilijardi"
msgstr[2] "kvadrilijarde"
msgstr[3] "kvadrilijard"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "kvintilijon"
msgstr[1] "kvintilijona"
msgstr[2] "kvintilijoni"
msgstr[3] "kvintilijonov"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "kvintilijarda"
msgstr[1] "kvintilijardi"
msgstr[2] "kvintilijarde"
msgstr[3] "kvintilijard"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "gugol"
msgstr[1] "gugola"
msgstr[2] "gugoli"
msgstr[3] "gugolov"

#: src/humanize/number.py:301
msgid "zero"
msgstr "nič"

#: src/humanize/number.py:302
msgid "one"
msgstr "ena"

#: src/humanize/number.py:303
msgid "two"
msgstr "dve"

#: src/humanize/number.py:304
msgid "three"
msgstr "tri"

#: src/humanize/number.py:305
msgid "four"
msgstr "štiri"

#: src/humanize/number.py:306
msgid "five"
msgstr "pet"

#: src/humanize/number.py:307
msgid "six"
msgstr "šest"

#: src/humanize/number.py:308
msgid "seven"
msgstr "sedem"

#: src/humanize/number.py:309
msgid "eight"
msgstr "osem"

#: src/humanize/number.py:310
msgid "nine"
msgstr "devet"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d mikrosekunda"
msgstr[1] "%d mikrosekundi"
msgstr[2] "%d mikrosekunde"
msgstr[3] "%d mikrosekund"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d milisekunda"
msgstr[1] "%d milisekundi"
msgstr[2] "%d milisekunde"
msgstr[3] "%d milisekund"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "trenutek"

#: src/humanize/time.py:167
msgid "a second"
msgstr "sekunda"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d sekunda"
msgstr[1] "%d sekundi"
msgstr[2] "%d sekunde"
msgstr[3] "%d sekund"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "minuta"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minuta"
msgstr[1] "%d minuti"
msgstr[2] "%d minute"
msgstr[3] "%d minut"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "ura"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d ura"
msgstr[1] "%d uri"
msgstr[2] "%d ure"
msgstr[3] "%d ur"

#: src/humanize/time.py:188
msgid "a day"
msgstr "dan"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dan"
msgstr[1] "%d dneva"
msgstr[2] "%d dnevi"
msgstr[3] "%d dni"

#: src/humanize/time.py:197
msgid "a month"
msgstr "mesec"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d mesec"
msgstr[1] "%d meseca"
msgstr[2] "%d meseci"
msgstr[3] "%d mesecev"

#: src/humanize/time.py:203
msgid "a year"
msgstr "leto"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 leto, %d dan"
msgstr[1] "1 leto, %d dneva"
msgstr[2] "1 leto, %d dnevi"
msgstr[3] "1 leto, %d dni"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1 leto, 1 mesec"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 leto, %d mesec"
msgstr[1] "1 leto, %d meseca"
msgstr[2] "1 leto, %d meseci"
msgstr[3] "1 leto, %d mesecev"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d leto"
msgstr[1] "%d leti"
msgstr[2] "%d leta"
msgstr[3] "%d let"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "%s od zdaj"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s nazaj"

#: src/humanize/time.py:260
msgid "now"
msgstr "zdaj"

#: src/humanize/time.py:284
msgid "today"
msgstr "danes"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "jutri"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "včeraj"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s in %s"
