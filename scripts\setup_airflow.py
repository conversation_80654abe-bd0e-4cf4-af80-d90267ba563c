#!/usr/bin/env python3
"""
Airflow设置脚本 - 针对Windows环境的兼容性处理
"""

import os
import subprocess
import sys
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
AIRFLOW_HOME = PROJECT_ROOT / "airflow"


def setup_airflow_environment():
    """设置Airflow环境变量"""
    os.environ["AIRFLOW_HOME"] = str(AIRFLOW_HOME)
    os.environ["AIRFLOW__CORE__DAGS_FOLDER"] = str(AIRFLOW_HOME / "dags")
    os.environ["AIRFLOW__CORE__BASE_LOG_FOLDER"] = str(AIRFLOW_HOME / "logs")
    os.environ["AIRFLOW__CORE__PLUGINS_FOLDER"] = str(AIRFLOW_HOME / "plugins")
    os.environ["AIRFLOW__CORE__EXECUTOR"] = "SequentialExecutor"  # Windows兼容
    os.environ["AIRFLOW__DATABASE__SQL_ALCHEMY_CONN"] = "sqlite:////" + str(
        AIRFLOW_HOME / "airflow.db"
    )
    os.environ["AIRFLOW__CORE__LOAD_EXAMPLES"] = "False"
    os.environ["AIRFLOW__WEBSERVER__EXPOSE_CONFIG"] = "True"
    os.environ["AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION"] = "True"

    print(f"AIRFLOW_HOME设置为: {AIRFLOW_HOME}")


def create_directories():
    """创建必要的目录"""
    directories = [
        AIRFLOW_HOME / "dags",
        AIRFLOW_HOME / "logs",
        AIRFLOW_HOME / "plugins",
        AIRFLOW_HOME / "config",
    ]

    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {directory}")


def init_airflow_db():
    """初始化Airflow数据库"""
    try:
        print("初始化Airflow数据库...")
        result = subprocess.run(
            [sys.executable, "-m", "airflow", "db", "init"],
            capture_output=True,
            text=True,
            cwd=PROJECT_ROOT,
        )

        if result.returncode == 0:
            print("Airflow数据库初始化成功!")
        else:
            print(f"数据库初始化失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"初始化数据库时出错: {e}")
        return False

    return True


def create_admin_user():
    """创建管理员用户"""
    try:
        print("创建管理员用户...")
        result = subprocess.run(
            [
                sys.executable,
                "-m",
                "airflow",
                "users",
                "create",
                "--username",
                "admin",
                "--firstname",
                "Admin",
                "--lastname",
                "User",
                "--role",
                "Admin",
                "--email",
                "<EMAIL>",
                "--password",
                "admin123",
            ],
            capture_output=True,
            text=True,
            cwd=PROJECT_ROOT,
        )

        if result.returncode == 0:
            print("管理员用户创建成功!")
            print("用户名: admin")
            print("密码: admin123")
        else:
            print(f"用户创建失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"创建用户时出错: {e}")
        return False

    return True


def test_airflow_installation():
    """测试Airflow安装"""
    try:
        print("测试Airflow安装...")
        result = subprocess.run(
            [sys.executable, "-m", "airflow", "version"],
            capture_output=True,
            text=True,
            cwd=PROJECT_ROOT,
        )

        if result.returncode == 0:
            print(f"Airflow版本: {result.stdout.strip()}")
            return True
        else:
            print(f"Airflow测试失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"测试Airflow时出错: {e}")
        return False


def main():
    """主函数"""
    print("开始设置Airflow环境...")

    # 设置环境变量
    setup_airflow_environment()

    # 创建目录
    create_directories()

    # 测试安装
    if not test_airflow_installation():
        print("Airflow安装测试失败，请检查安装")
        return False

    # 初始化数据库
    if not init_airflow_db():
        print("数据库初始化失败")
        return False

    # 创建管理员用户
    if not create_admin_user():
        print("管理员用户创建失败")
        return False

    print("\nAirflow环境设置完成!")
    print("可以使用以下命令启动Airflow:")
    print("1. 启动webserver: uv run python scripts/start_airflow_webserver.py")
    print("2. 启动scheduler: uv run python scripts/start_airflow_scheduler.py")
    print("3. 访问Web UI: http://localhost:8080")

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
