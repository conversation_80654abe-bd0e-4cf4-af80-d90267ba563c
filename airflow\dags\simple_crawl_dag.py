"""
简单爬取DAG示例
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目路径到Python路径
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
sys.path.insert(0, os.path.join(project_root, "src"))

from data_trans.workflow.dag_templates import create_simple_crawl_dag

# DAG配置
DAG_ID = "simple_crawl_example"
TARGET_URLS = [
    "https://httpbin.org/json",
    "https://httpbin.org/uuid",
    "https://httpbin.org/ip",
]

# 爬虫配置
CRAWLER_CONFIG = {
    "timeout": 30,
    "max_retries": 3,
    "delay": 1,
    "user_agent": "DataTrans-SimpleCrawl/1.0",
    "headers": {"Accept": "application/json"},
}

# 清洗配置
CLEANER_CONFIG = {
    "remove_html": True,
    "normalize_whitespace": True,
    "remove_empty": True,
    "extract_json": True,
}

# 存储配置
STORAGE_CONFIG = {
    "type": "mongodb",
    "collection": "simple_crawl_data",
    "database": "datatrans",
}

# 创建DAG
dag = create_simple_crawl_dag(
    dag_id=DAG_ID,
    target_urls=TARGET_URLS,
    crawler_config=CRAWLER_CONFIG,
    cleaner_config=CLEANER_CONFIG,
    storage_config=STORAGE_CONFIG,
    schedule_interval="@daily",  # 每天执行一次
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=["example", "simple", "crawl"],
)

# 导出DAG供Airflow使用
globals()[DAG_ID] = dag
