{".class": "MypyFile", "_fullname": "email.mime.multipart", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "MIMEBase": {".class": "SymbolTableNode", "cross_ref": "email.mime.base.MIMEBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MIMEMultipart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.mime.base.MIMEBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.mime.multipart.MIMEMultipart", "name": "MIMEMultipart", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.mime.multipart.MIMEMultipart", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.mime.multipart", "mro": ["email.mime.multipart.MIMEMultipart", "email.mime.base.MIMEBase", "email.message.Message", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 5, 4], "arg_names": ["self", "_subtype", "boundary", "_subparts", "policy", "_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "email.mime.multipart.MIMEMultipart.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 5, 4], "arg_names": ["self", "_subtype", "boundary", "_subparts", "policy", "_params"], "arg_types": ["email.mime.multipart.MIMEMultipart", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email.policy._MessageT", "id": -1, "name": "_MessageT", "namespace": "email.mime.multipart.MIMEMultipart.__init__", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email.policy._MessageT", "id": -1, "name": "_MessageT", "namespace": "email.mime.multipart.MIMEMultipart.__init__", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "email._ParamsType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MIMEMultipart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email.policy._MessageT", "id": -1, "name": "_MessageT", "namespace": "email.mime.multipart.MIMEMultipart.__init__", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email.mime.multipart.MIMEMultipart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "email.mime.multipart.MIMEMultipart", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Policy": {".class": "SymbolTableNode", "cross_ref": "email._policybase.Policy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_MessageT": {".class": "SymbolTableNode", "cross_ref": "email.policy._MessageT", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ParamsType": {".class": "SymbolTableNode", "cross_ref": "email._ParamsType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email.mime.multipart.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.mime.multipart.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.mime.multipart.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.mime.multipart.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.mime.multipart.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.mime.multipart.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.mime.multipart.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\email\\mime\\multipart.pyi"}