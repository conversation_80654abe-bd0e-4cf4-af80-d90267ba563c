{"data_mtime": 1752408380, "dep_lines": [15, 16, 230, 5, 6, 7, 8, 9, 10, 14, 227, 228, 229, 230, 1, 1, 177], "dep_prios": [5, 5, 20, 10, 10, 10, 5, 5, 5, 10, 20, 20, 20, 20, 5, 30, 20], "dependencies": ["email.mime.multipart", "email.mime.text", "urllib.parse", "asyncio", "json", "logging", "abc", "datetime", "typing", "smtplib", "base64", "<PERSON><PERSON><PERSON>", "hmac", "urllib", "builtins", "_frozen_importlib"], "hash": "e88c4c287db432b8e81fd1df441e8c98b46600d2", "id": "src.data_trans.workflow.notifications", "ignore_all": false, "interface_hash": "50dcd561650e313dc5b9809b39342af0cb8e6866", "mtime": 1752408378, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src\\data_trans\\workflow\\notifications.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 15293, "suppressed": ["httpx"], "version_id": "1.16.1"}