{"data_mtime": 1752408380, "dep_lines": [13, 18, 5, 6, 7, 8, 9, 10, 118, 1, 1, 1, 1, 1, 1, 19, 15, 137], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 5, 5, 20], "dependencies": ["src.data_trans.workflow.base", "src.data_trans.config.settings", "asyncio", "logging", "os", "datetime", "pathlib", "typing", "random", "builtins", "_frozen_importlib", "_typeshed", "abc", "src.data_trans.config", "typing_extensions"], "hash": "9e975074057725abc50684f8254eac0aef109476", "id": "src.data_trans.workflow.sensors", "ignore_all": false, "interface_hash": "127520427a18ec22e2d1a4c9ccfe8de2a915ca1d", "mtime": 1752408378, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src\\data_trans\\workflow\\sensors.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 11780, "suppressed": ["src.data_trans.queue.redis_queue", "base", "httpx"], "version_id": "1.16.1"}