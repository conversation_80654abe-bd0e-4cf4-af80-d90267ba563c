{"variables": [{"key": "crawler_config", "value": {"timeout": 30, "max_retries": 3, "delay": 1, "user_agent": "DataTrans/1.0"}, "description": "默认爬虫配置", "is_encrypted": false, "created_at": "2025-07-13T16:17:13.386913", "updated_at": "2025-07-13T16:17:13.386913"}, {"key": "storage_config", "value": {"type": "mongodb", "database": "datatrans", "collection": "crawl_data"}, "description": "默认存储配置", "is_encrypted": false, "created_at": "2025-07-13T16:17:13.386913", "updated_at": "2025-07-13T16:17:13.386913"}, {"key": "notification_config", "value": {"channels": ["log", "email"], "email_recipients": ["<EMAIL>"], "slack_webhook": ""}, "description": "默认通知配置", "is_encrypted": false, "created_at": "2025-07-13T16:17:13.386913", "updated_at": "2025-07-13T16:17:13.386913"}, {"key": "system_settings", "value": {"max_concurrent_tasks": 10, "task_timeout": 3600, "retry_delay": 300}, "description": "系统设置", "is_encrypted": false, "created_at": "2025-07-13T16:17:13.386913", "updated_at": "2025-07-13T16:17:13.386913"}, {"key": "api_keys", "value": {"openai_api_key": "", "google_api_key": "", "github_token": ""}, "description": "API密钥配置", "is_encrypted": true, "created_at": "2025-07-13T16:17:13.386913", "updated_at": "2025-07-13T16:17:13.386913"}, {"key": "test_variable", "value": {"key": "value"}, "description": "测试变量", "is_encrypted": false, "created_at": "2025-07-13T16:17:13.387918", "updated_at": "2025-07-13T16:17:13.387918"}], "updated_at": "2025-07-13T16:17:13.387918"}