"""
批量处理DAG示例
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目路径到Python路径
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
sys.path.insert(0, os.path.join(project_root, "src"))

from data_trans.workflow.dag_templates import create_batch_process_dag

# DAG配置
DAG_ID = "batch_process_example"

# URL批次配置
URL_BATCHES = [
    ["https://httpbin.org/json", "https://httpbin.org/uuid", "https://httpbin.org/ip"],
    [
        "https://httpbin.org/user-agent",
        "https://httpbin.org/headers",
        "https://httpbin.org/get",
    ],
    [
        "https://httpbin.org/status/200",
        "https://httpbin.org/delay/1",
        "https://httpbin.org/base64/SFRUUEJJTiBpcyBhd2Vzb21l",
    ],
]

# 爬虫配置
CRAWLER_CONFIG = {
    "timeout": 30,
    "max_retries": 3,
    "delay": 0.5,
    "concurrent_requests": 5,
    "user_agent": "DataTrans-BatchCrawl/1.0",
    "headers": {"Accept": "application/json"},
}

# 清洗配置
CLEANER_CONFIG = {
    "remove_html": True,
    "normalize_whitespace": True,
    "remove_empty": True,
    "extract_json": True,
    "batch_size": 100,
    "parallel_processing": True,
}

# 存储配置
STORAGE_CONFIG = {
    "type": "mongodb",
    "collection": "batch_process_data",
    "database": "datatrans",
    "batch_insert": True,
    "batch_size": 50,
}

# 创建DAG
dag = create_batch_process_dag(
    dag_id=DAG_ID,
    url_batches=URL_BATCHES,
    crawler_config=CRAWLER_CONFIG,
    cleaner_config=CLEANER_CONFIG,
    storage_config=STORAGE_CONFIG,
    schedule_interval="0 2 * * *",  # 每天凌晨2点执行
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=["example", "batch", "parallel"],
)

# 导出DAG供Airflow使用
globals()[DAG_ID] = dag
