"""
数据采集任务执行器模块

该模块实现完整的数据采集到存储流程，整合爬虫、清洗、存储功能。
"""

__version__ = "0.1.0"
__author__ = "Data Trans Team"

# 导入主要的执行器类
from .base_executor import BaseExecutor, ExecutorConfig

# 导入测试和验证模块
from .python311_compatibility import (
    PerformanceBenchmark,
    Python311Features,
    Python311Validator,
)

# 导入Ray相关模块（可选导入，避免Windows兼容性问题）
try:
    from .ray_actors import ResourceMonitorActor, TaskManagerActor
    from .ray_cluster_manager import (
        ClusterHealth,
        ClusterStatus,
        NodeStatus,
        RayClusterManager,
    )
    from .ray_config import (
        RayActorResourceConfig,
        RayClusterConfig,
        RayConfigManager,
        RayPerformanceConfig,
        RayTaskResourceConfig,
        get_ray_config_manager,
    )
    from .ray_executor import RayExecutor, RayExecutorConfig, RayTaskResult
    from .ray_integration_test import RayIntegrationTestSuite
    from .ray_resource_manager import (
        AutoScalingConfig,
        RayResourceManager,
        ResourceAllocation,
        ResourceUsage,
    )
    from .ray_state_manager import (
        DistributedStateActor,
        RayStateManager,
        StateEntry,
        StateSnapshot,
    )
    from .ray_task_executor import RayTaskExecutor, RayTaskExecutorConfig

    RAY_AVAILABLE = True
except ImportError as e:
    import warnings

    warnings.warn(f"Ray模块导入失败（可能是Windows兼容性问题）: {e}")

    # 创建占位符类
    class _RayPlaceholder:
        def __init__(self, *args, **kwargs):
            raise ImportError("Ray模块不可用，请在支持的平台上使用Ray功能")

    # 为所有Ray相关类创建占位符
    ResourceMonitorActor = _RayPlaceholder
    TaskManagerActor = _RayPlaceholder
    ClusterHealth = _RayPlaceholder
    ClusterStatus = _RayPlaceholder
    NodeStatus = _RayPlaceholder
    RayClusterManager = _RayPlaceholder
    RayActorResourceConfig = _RayPlaceholder
    RayClusterConfig = _RayPlaceholder
    RayConfigManager = _RayPlaceholder
    RayPerformanceConfig = _RayPlaceholder
    RayTaskResourceConfig = _RayPlaceholder
    get_ray_config_manager = _RayPlaceholder
    RayExecutor = _RayPlaceholder
    RayExecutorConfig = _RayPlaceholder
    RayTaskResult = _RayPlaceholder
    RayIntegrationTestSuite = _RayPlaceholder
    AutoScalingConfig = _RayPlaceholder
    RayResourceManager = _RayPlaceholder
    ResourceAllocation = _RayPlaceholder
    ResourceUsage = _RayPlaceholder
    DistributedStateActor = _RayPlaceholder
    RayStateManager = _RayPlaceholder
    StateEntry = _RayPlaceholder
    StateSnapshot = _RayPlaceholder
    RayTaskExecutor = _RayPlaceholder
    RayTaskExecutorConfig = _RayPlaceholder

    RAY_AVAILABLE = False
from .task_executor import TaskExecutor, TaskExecutorConfig

__all__: list[str] = [
    "BaseExecutor",
    "ExecutorConfig",
    "TaskExecutor",
    "TaskExecutorConfig",
    "RayExecutor",
    "RayExecutorConfig",
    "RayTaskResult",
    "RayTaskExecutor",
    "RayTaskExecutorConfig",
    "TaskManagerActor",
    "ResourceMonitorActor",
    "RayClusterConfig",
    "RayTaskResourceConfig",
    "RayActorResourceConfig",
    "RayPerformanceConfig",
    "RayConfigManager",
    "get_ray_config_manager",
    "RayResourceManager",
    "ResourceUsage",
    "ResourceAllocation",
    "AutoScalingConfig",
    "RayClusterManager",
    "ClusterStatus",
    "NodeStatus",
    "ClusterHealth",
    "RayStateManager",
    "DistributedStateActor",
    "StateEntry",
    "StateSnapshot",
    "Python311Validator",
    "Python311Features",
    "PerformanceBenchmark",
    "RayIntegrationTestSuite",
]
