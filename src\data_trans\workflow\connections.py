"""
连接管理 - 模拟Airflow的连接管理功能
"""

import json
import logging
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class Connection:
    """连接配置"""

    conn_id: str
    conn_type: str
    host: Optional[str] = None
    port: Optional[int] = None
    schema: Optional[str] = None
    login: Optional[str] = None
    password: Optional[str] = None
    extra: Optional[Dict[str, Any]] = None
    description: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Connection":
        """从字典创建连接"""
        return cls(**data)

    def get_uri(self) -> str:
        """获取连接URI"""
        if self.conn_type == "postgres":
            return f"postgresql://{self.login}:{self.password}@{self.host}:{self.port}/{self.schema}"
        elif self.conn_type == "redis":
            return (
                f"redis://:{self.password}@{self.host}:{self.port}/{self.schema or 0}"
            )
        elif self.conn_type == "mongodb":
            return f"mongodb://{self.login}:{self.password}@{self.host}:{self.port}/{self.schema}"
        elif self.conn_type == "http":
            protocol = "https" if self.port == 443 else "http"
            return f"{protocol}://{self.host}:{self.port}"
        else:
            return f"{self.conn_type}://{self.host}:{self.port}"


class ConnectionManager:
    """连接管理器"""

    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "airflow/config/connections.json"
        self.connections: Dict[str, Connection] = {}
        self._load_connections()

    def _load_connections(self):
        """加载连接配置"""
        config_path = Path(self.config_file)
        if config_path.exists():
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    for conn_data in data.get("connections", []):
                        conn = Connection.from_dict(conn_data)
                        self.connections[conn.conn_id] = conn
                logger.info(f"加载了 {len(self.connections)} 个连接配置")
            except Exception as e:
                logger.error(f"加载连接配置失败: {e}")
        else:
            logger.info("连接配置文件不存在，将创建默认配置")
            self._create_default_connections()

    def _save_connections(self):
        """保存连接配置"""
        config_path = Path(self.config_file)
        config_path.parent.mkdir(parents=True, exist_ok=True)

        data = {
            "connections": [conn.to_dict() for conn in self.connections.values()],
            "updated_at": datetime.now().isoformat(),
        }

        try:
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"保存连接配置到: {config_path}")
        except Exception as e:
            logger.error(f"保存连接配置失败: {e}")

    def _create_default_connections(self):
        """创建默认连接"""
        default_connections = [
            Connection(
                conn_id="postgres_default",
                conn_type="postgres",
                host="localhost",
                port=5432,
                schema="datatrans",
                login="postgres",
                password="postgres",
                description="默认PostgreSQL连接",
            ),
            Connection(
                conn_id="redis_default",
                conn_type="redis",
                host="localhost",
                port=6379,
                schema="0",
                password="",
                description="默认Redis连接",
            ),
            Connection(
                conn_id="mongodb_default",
                conn_type="mongodb",
                host="localhost",
                port=27017,
                schema="datatrans",
                login="",
                password="",
                description="默认MongoDB连接",
            ),
            Connection(
                conn_id="http_default",
                conn_type="http",
                host="httpbin.org",
                port=443,
                description="默认HTTP连接",
            ),
        ]

        for conn in default_connections:
            self.connections[conn.conn_id] = conn

        self._save_connections()

    def add_connection(self, connection: Connection) -> bool:
        """添加连接"""
        try:
            self.connections[connection.conn_id] = connection
            self._save_connections()
            logger.info(f"添加连接: {connection.conn_id}")
            return True
        except Exception as e:
            logger.error(f"添加连接失败: {e}")
            return False

    def get_connection(self, conn_id: str) -> Optional[Connection]:
        """获取连接"""
        return self.connections.get(conn_id)

    def update_connection(self, conn_id: str, **kwargs) -> bool:
        """更新连接"""
        if conn_id not in self.connections:
            logger.error(f"连接不存在: {conn_id}")
            return False

        try:
            conn = self.connections[conn_id]
            for key, value in kwargs.items():
                if hasattr(conn, key):
                    setattr(conn, key, value)
            conn.updated_at = datetime.now().isoformat()

            self._save_connections()
            logger.info(f"更新连接: {conn_id}")
            return True
        except Exception as e:
            logger.error(f"更新连接失败: {e}")
            return False

    def delete_connection(self, conn_id: str) -> bool:
        """删除连接"""
        if conn_id not in self.connections:
            logger.error(f"连接不存在: {conn_id}")
            return False

        try:
            del self.connections[conn_id]
            self._save_connections()
            logger.info(f"删除连接: {conn_id}")
            return True
        except Exception as e:
            logger.error(f"删除连接失败: {e}")
            return False

    def list_connections(self) -> List[Connection]:
        """列出所有连接"""
        return list(self.connections.values())

    def test_connection(self, conn_id: str) -> bool:
        """测试连接"""
        conn = self.get_connection(conn_id)
        if not conn:
            logger.error(f"连接不存在: {conn_id}")
            return False

        try:
            if conn.conn_type == "postgres":
                return self._test_postgres_connection(conn)
            elif conn.conn_type == "redis":
                return self._test_redis_connection(conn)
            elif conn.conn_type == "mongodb":
                return self._test_mongodb_connection(conn)
            elif conn.conn_type == "http":
                return self._test_http_connection(conn)
            else:
                logger.warning(f"不支持测试连接类型: {conn.conn_type}")
                return False
        except Exception as e:
            logger.error(f"测试连接失败: {conn_id}, 错误: {e}")
            return False

    def _test_postgres_connection(self, conn: Connection) -> bool:
        """测试PostgreSQL连接"""
        try:
            import psycopg2

            connection = psycopg2.connect(
                host=conn.host,
                port=conn.port,
                database=conn.schema,
                user=conn.login,
                password=conn.password,
            )
            connection.close()
            logger.info(f"PostgreSQL连接测试成功: {conn.conn_id}")
            return True
        except Exception as e:
            logger.error(f"PostgreSQL连接测试失败: {conn.conn_id}, 错误: {e}")
            return False

    def _test_redis_connection(self, conn: Connection) -> bool:
        """测试Redis连接"""
        try:
            import redis

            r = redis.Redis(
                host=conn.host,
                port=conn.port,
                db=int(conn.schema or 0),
                password=conn.password if conn.password else None,
            )
            r.ping()
            logger.info(f"Redis连接测试成功: {conn.conn_id}")
            return True
        except Exception as e:
            logger.error(f"Redis连接测试失败: {conn.conn_id}, 错误: {e}")
            return False

    def _test_mongodb_connection(self, conn: Connection) -> bool:
        """测试MongoDB连接"""
        try:
            from pymongo import MongoClient

            client = MongoClient(
                host=conn.host,
                port=conn.port,
                username=conn.login if conn.login else None,
                password=conn.password if conn.password else None,
            )
            client.admin.command("ping")
            client.close()
            logger.info(f"MongoDB连接测试成功: {conn.conn_id}")
            return True
        except Exception as e:
            logger.error(f"MongoDB连接测试失败: {conn.conn_id}, 错误: {e}")
            return False

    def _test_http_connection(self, conn: Connection) -> bool:
        """测试HTTP连接"""
        try:
            import httpx

            url = conn.get_uri()
            response = httpx.get(url, timeout=10)
            response.raise_for_status()
            logger.info(f"HTTP连接测试成功: {conn.conn_id}")
            return True
        except Exception as e:
            logger.error(f"HTTP连接测试失败: {conn.conn_id}, 错误: {e}")
            return False


# 全局连接管理器实例
_connection_manager: Optional[ConnectionManager] = None


def get_connection_manager() -> ConnectionManager:
    """获取连接管理器实例"""
    global _connection_manager
    if _connection_manager is None:
        _connection_manager = ConnectionManager()
    return _connection_manager


def get_connection(conn_id: str) -> Optional[Connection]:
    """获取连接（便捷函数）"""
    return get_connection_manager().get_connection(conn_id)
