# Language tlh translations for humanize package.
# Copyright (C) 2023
# This file is distributed under the same license as the humanize package.
# <PERSON> <<EMAIL>>, 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-05 14:07-0400\n"
"PO-Revision-Date: 2023-10-05 14:11-0400\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Language tlh\n"
"Language: tlh\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: src/humanize/number.py:83
msgctxt "0 (male)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:84
msgctxt "1 (male)"
msgid "st"
msgstr "-DIch"

#: src/humanize/number.py:85
msgctxt "2 (male)"
msgid "nd"
msgstr "-DIch"

#: src/humanize/number.py:86
msgctxt "3 (male)"
msgid "rd"
msgstr "-DIch"

#: src/humanize/number.py:87
msgctxt "4 (male)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:88
msgctxt "5 (male)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:89
msgctxt "6 (male)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:90
msgctxt "7 (male)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:91
msgctxt "8 (male)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:92
msgctxt "9 (male)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:96
msgctxt "0 (female)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:97
msgctxt "1 (female)"
msgid "st"
msgstr "-DIch"

#: src/humanize/number.py:98
msgctxt "2 (female)"
msgid "nd"
msgstr "-DIch"

#: src/humanize/number.py:99
msgctxt "3 (female)"
msgid "rd"
msgstr "-DIch"

#: src/humanize/number.py:100
msgctxt "4 (female)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:101
msgctxt "5 (female)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:102
msgctxt "6 (female)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:103
msgctxt "7 (female)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:104
msgctxt "8 (female)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:105
msgctxt "9 (female)"
msgid "th"
msgstr "-DIch"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "SaD"
msgstr[1] "SaD"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "ʼuyʼ"
msgstr[1] "ʼuyʼ"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "Saghan"
msgstr[1] "Saghan"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "SaDSaghan"
msgstr[1] "SaDSaghan"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:304
msgid "zero"
msgstr "pagh"

#: src/humanize/number.py:305
msgid "one"
msgstr "waʼ"

#: src/humanize/number.py:306
msgid "two"
msgstr "chaʼ"

#: src/humanize/number.py:307
msgid "three"
msgstr "wej"

#: src/humanize/number.py:308
msgid "four"
msgstr "loS"

#: src/humanize/number.py:309
msgid "five"
msgstr "vagh"

#: src/humanize/number.py:310
msgid "six"
msgstr "jav"

#: src/humanize/number.py:311
msgid "seven"
msgstr "Soch"

#: src/humanize/number.py:312
msgid "eight"
msgstr "chorgh"

#: src/humanize/number.py:313
msgid "nine"
msgstr "Hut"

#: src/humanize/time.py:151
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "waʼ ʼuyʼ loch %d lup"
msgstr[1] "waʼ ʼuyʼ loch %d lup"

#: src/humanize/time.py:160
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "waʼ SaD loch %d lup"
msgstr[1] "waʼ SaD loch %d lup"

#: src/humanize/time.py:163 src/humanize/time.py:262
msgid "a moment"
msgstr ""

#: src/humanize/time.py:166
msgid "a second"
msgstr "waʼ lup"

#: src/humanize/time.py:169
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d lup"
msgstr[1] "%d lup"

#: src/humanize/time.py:172
msgid "a minute"
msgstr "waʼ tup"

#: src/humanize/time.py:176
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d tup"
msgstr[1] "%d tup"

#: src/humanize/time.py:179
msgid "an hour"
msgstr "waʼ rep"

#: src/humanize/time.py:183
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d rep"
msgstr[1] "%d rep"

#: src/humanize/time.py:187
msgid "a day"
msgstr "waʼ jaj"

#: src/humanize/time.py:190 src/humanize/time.py:193
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d jaj"
msgstr[1] "%d jaj"

#: src/humanize/time.py:196
msgid "a month"
msgstr "waʼ jar"

#: src/humanize/time.py:198
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d jar"
msgstr[1] "%d jar"

#: src/humanize/time.py:202
msgid "a year"
msgstr "waʼ DIS"

#: src/humanize/time.py:205 src/humanize/time.py:216
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "waʼ DIS, %d jaj"
msgstr[1] "waʼ DIS, %d jaj"

#: src/humanize/time.py:209
msgid "1 year, 1 month"
msgstr "waʼ DIS, wa jar"

#: src/humanize/time.py:212
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "waʼ DIS, %d jar"
msgstr[1] "waʼ DIS, %d jar"

#: src/humanize/time.py:218
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d DIS"
msgstr[1] "%d DIS"

#: src/humanize/time.py:259
#, python-format
msgid "%s from now"
msgstr "%s pIq"

#: src/humanize/time.py:259
#, python-format
msgid "%s ago"
msgstr "%s ret"

#: src/humanize/time.py:263
msgid "now"
msgstr "DaH"

#: src/humanize/time.py:296
msgid "today"
msgstr "DaHjaj"

#: src/humanize/time.py:299
msgid "tomorrow"
msgstr "waʼleS"

#: src/humanize/time.py:302
msgid "yesterday"
msgstr "waʼHuʼ"

#: src/humanize/time.py:612
#, python-format
msgid "%s and %s"
msgstr "%s %s je"
