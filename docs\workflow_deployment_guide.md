# DataTrans 工作流系统部署指南

## 概述

本指南详细介绍如何在不同环境中部署和配置 DataTrans 工作流系统。

## 系统要求

### 最低要求
- Python 3.11+
- 内存: 2GB+
- 磁盘空间: 10GB+
- 网络: 稳定的互联网连接

### 推荐配置
- Python 3.11+
- 内存: 8GB+
- 磁盘空间: 50GB+
- CPU: 4核+
- 网络: 高速互联网连接

### 依赖服务（可选）
- MongoDB: 数据存储
- PostgreSQL: 关系型数据存储
- Redis: 队列和缓存
- SMTP服务器: 邮件通知

## 安装步骤

### 1. 环境准备

#### 使用 uv（推荐）
```bash
# 安装 uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 克隆项目
git clone https://github.com/your-org/data_trans.git
cd data_trans

# 安装依赖
uv sync
```

#### 使用 pip
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 基础配置

#### 创建配置目录
```bash
mkdir -p airflow/config
mkdir -p airflow/dags
mkdir -p airflow/logs
mkdir -p airflow/plugins
```

#### 环境变量配置
创建 `.env` 文件：
```bash
# 工作流配置
WORKFLOW_HOME=/path/to/data_trans/airflow
WORKFLOW_DAGS_FOLDER=/path/to/data_trans/airflow/dags
WORKFLOW_LOGS_FOLDER=/path/to/data_trans/airflow/logs

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/datatrans
POSTGRES_URI=postgresql://user:password@localhost:5432/datatrans
REDIS_URI=redis://localhost:6379/0

# 通知配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
NOTIFICATION_EMAIL=<EMAIL>

# Slack配置（可选）
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...

# 钉钉配置（可选）
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=...
DINGTALK_SECRET=your_secret
```

### 3. 初始化配置

运行初始化脚本：
```bash
uv run python scripts/init_workflow.py
```

或手动配置：
```python
from data_trans.workflow.config_manager import get_config_manager

config_manager = get_config_manager()

# 设置默认连接
config_manager.add_connection(
    conn_id="mongodb_default",
    conn_type="mongodb",
    host="localhost",
    port=27017,
    schema="datatrans"
)

config_manager.add_connection(
    conn_id="postgres_default",
    conn_type="postgres",
    host="localhost",
    port=5432,
    schema="datatrans",
    login="user",
    password="password"
)

# 设置默认变量
config_manager.set_variable("default_timeout", 30)
config_manager.set_variable("max_retries", 3)
```

## 部署模式

### 1. 开发环境

#### 单机部署
```bash
# 启动工作流系统
uv run python examples/workflow_example.py

# 或者启动调度器
uv run python scripts/start_scheduler.py
```

#### 配置文件
创建 `config/development.py`：
```python
# 开发环境配置
DEBUG = True
LOG_LEVEL = "DEBUG"

# 数据库配置
MONGODB_URI = "mongodb://localhost:27017/datatrans_dev"
POSTGRES_URI = "postgresql://localhost:5432/datatrans_dev"

# 通知配置
NOTIFICATION_CHANNELS = ["log"]  # 仅日志通知

# 调度配置
SCHEDULER_INTERVAL = 30  # 30秒检查一次
```

### 2. 测试环境

#### Docker 部署
创建 `docker-compose.yml`：
```yaml
version: '3.8'

services:
  datatrans-workflow:
    build: .
    environment:
      - WORKFLOW_ENV=testing
      - MONGODB_URI=mongodb://mongo:27017/datatrans_test
      - REDIS_URI=redis://redis:6379/0
    volumes:
      - ./airflow:/app/airflow
      - ./logs:/app/logs
    depends_on:
      - mongo
      - redis
    ports:
      - "8080:8080"

  mongo:
    image: mongo:5.0
    environment:
      MONGO_INITDB_DATABASE: datatrans_test
    volumes:
      - mongo_data:/data/db
    ports:
      - "27017:27017"

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"

volumes:
  mongo_data:
  redis_data:
```

启动服务：
```bash
docker-compose up -d
```

#### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装 uv
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.cargo/bin:$PATH"

# 复制项目文件
COPY . .

# 安装Python依赖
RUN uv sync

# 创建必要目录
RUN mkdir -p airflow/config airflow/dags airflow/logs airflow/plugins

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["uv", "run", "python", "scripts/start_scheduler.py"]
```

### 3. 生产环境

#### 系统服务部署

创建 systemd 服务文件 `/etc/systemd/system/datatrans-workflow.service`：
```ini
[Unit]
Description=DataTrans Workflow Scheduler
After=network.target

[Service]
Type=simple
User=datatrans
Group=datatrans
WorkingDirectory=/opt/datatrans
Environment=PATH=/opt/datatrans/venv/bin
ExecStart=/opt/datatrans/venv/bin/python scripts/start_scheduler.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable datatrans-workflow
sudo systemctl start datatrans-workflow
```

#### 负载均衡配置

使用 Nginx 作为反向代理：
```nginx
upstream datatrans_workflow {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
}

server {
    listen 80;
    server_name workflow.company.com;

    location / {
        proxy_pass http://datatrans_workflow;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件
    location /static/ {
        alias /opt/datatrans/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 数据库配置

**MongoDB 集群配置：**
```javascript
// 初始化副本集
rs.initiate({
  _id: "datatrans-rs",
  members: [
    { _id: 0, host: "mongo1:27017" },
    { _id: 1, host: "mongo2:27017" },
    { _id: 2, host: "mongo3:27017" }
  ]
});
```

**PostgreSQL 主从配置：**
```sql
-- 主库配置
ALTER SYSTEM SET wal_level = replica;
ALTER SYSTEM SET max_wal_senders = 3;
ALTER SYSTEM SET wal_keep_segments = 64;
SELECT pg_reload_conf();
```

## 监控和日志

### 1. 日志配置

创建 `logging.conf`：
```ini
[loggers]
keys=root,workflow,scheduler,notifications

[handlers]
keys=consoleHandler,fileHandler,rotatingFileHandler

[formatters]
keys=simpleFormatter,detailedFormatter

[logger_root]
level=INFO
handlers=consoleHandler,rotatingFileHandler

[logger_workflow]
level=DEBUG
handlers=fileHandler
qualname=workflow
propagate=0

[logger_scheduler]
level=INFO
handlers=fileHandler
qualname=scheduler
propagate=0

[logger_notifications]
level=INFO
handlers=fileHandler
qualname=notifications
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=FileHandler
level=DEBUG
formatter=detailedFormatter
args=('logs/workflow.log',)

[handler_rotatingFileHandler]
class=handlers.RotatingFileHandler
level=INFO
formatter=detailedFormatter
args=('logs/workflow.log', 'a', 10485760, 5)

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s

[formatter_detailedFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s
```

### 2. 监控指标

#### Prometheus 配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'datatrans-workflow'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

#### 自定义指标
```python
from prometheus_client import Counter, Histogram, Gauge

# 任务执行计数器
task_counter = Counter('workflow_tasks_total', 'Total number of tasks executed', ['dag_id', 'task_id', 'status'])

# 任务执行时间
task_duration = Histogram('workflow_task_duration_seconds', 'Task execution duration', ['dag_id', 'task_id'])

# 活跃DAG数量
active_dags = Gauge('workflow_active_dags', 'Number of active DAGs')
```

### 3. 告警配置

#### Alertmanager 规则
```yaml
# alert_rules.yml
groups:
  - name: datatrans_workflow
    rules:
      - alert: WorkflowTaskFailure
        expr: increase(workflow_tasks_total{status="failed"}[5m]) > 0
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "Workflow task failed"
          description: "Task {{ $labels.task_id }} in DAG {{ $labels.dag_id }} failed"

      - alert: WorkflowSchedulerDown
        expr: up{job="datatrans-workflow"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Workflow scheduler is down"
          description: "The workflow scheduler has been down for more than 1 minute"
```

## 安全配置

### 1. 网络安全

#### 防火墙配置
```bash
# 允许必要端口
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 8080/tcp  # Workflow UI

# 限制数据库访问
sudo ufw allow from 10.0.0.0/8 to any port 27017  # MongoDB
sudo ufw allow from 10.0.0.0/8 to any port 5432   # PostgreSQL

sudo ufw enable
```

#### SSL/TLS 配置
```nginx
server {
    listen 443 ssl http2;
    server_name workflow.company.com;

    ssl_certificate /etc/ssl/certs/workflow.crt;
    ssl_certificate_key /etc/ssl/private/workflow.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://datatrans_workflow;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. 数据安全

#### 敏感信息加密
```python
from cryptography.fernet import Fernet

# 生成加密密钥
key = Fernet.generate_key()
cipher_suite = Fernet(key)

# 加密敏感变量
encrypted_password = cipher_suite.encrypt(b"sensitive_password")

# 存储加密后的值
config_manager.set_variable(
    "database_password",
    encrypted_password.decode(),
    is_encrypted=True
)
```

#### 访问控制
```python
# 实现基于角色的访问控制
class RoleBasedAccess:
    def __init__(self):
        self.roles = {
            "admin": ["read", "write", "execute", "delete"],
            "operator": ["read", "execute"],
            "viewer": ["read"]
        }

    def check_permission(self, user_role: str, action: str) -> bool:
        return action in self.roles.get(user_role, [])
```

## 备份和恢复

### 1. 配置备份

#### 自动备份脚本
```bash
#!/bin/bash
# backup_config.sh

BACKUP_DIR="/backup/datatrans/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# 备份配置文件
cp -r airflow/config "$BACKUP_DIR/"
cp -r .env "$BACKUP_DIR/"

# 备份DAG文件
cp -r airflow/dags "$BACKUP_DIR/"

# 压缩备份
tar -czf "$BACKUP_DIR.tar.gz" -C /backup/datatrans "$(basename $BACKUP_DIR)"
rm -rf "$BACKUP_DIR"

# 清理旧备份（保留30天）
find /backup/datatrans -name "*.tar.gz" -mtime +30 -delete
```

#### 定时备份
```bash
# 添加到 crontab
0 2 * * * /opt/datatrans/scripts/backup_config.sh
```

### 2. 数据库备份

#### MongoDB 备份
```bash
#!/bin/bash
# backup_mongodb.sh

BACKUP_DIR="/backup/mongodb/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

mongodump --host localhost:27017 --db datatrans --out "$BACKUP_DIR"
tar -czf "$BACKUP_DIR.tar.gz" -C /backup/mongodb "$(basename $BACKUP_DIR)"
rm -rf "$BACKUP_DIR"
```

#### PostgreSQL 备份
```bash
#!/bin/bash
# backup_postgresql.sh

BACKUP_DIR="/backup/postgresql"
mkdir -p "$BACKUP_DIR"

pg_dump -h localhost -U postgres datatrans > "$BACKUP_DIR/datatrans_$(date +%Y%m%d_%H%M%S).sql"
gzip "$BACKUP_DIR/datatrans_$(date +%Y%m%d_%H%M%S).sql"
```

### 3. 恢复流程

#### 配置恢复
```bash
# 停止服务
sudo systemctl stop datatrans-workflow

# 恢复配置
tar -xzf /backup/datatrans/20240101_120000.tar.gz -C /tmp/
cp -r /tmp/20240101_120000/* /opt/datatrans/

# 重启服务
sudo systemctl start datatrans-workflow
```

#### 数据库恢复
```bash
# MongoDB 恢复
mongorestore --host localhost:27017 --db datatrans /backup/mongodb/20240101_120000/datatrans/

# PostgreSQL 恢复
gunzip -c /backup/postgresql/datatrans_20240101_120000.sql.gz | psql -h localhost -U postgres datatrans
```

## 性能优化

### 1. 系统优化

#### 内核参数调优
```bash
# /etc/sysctl.conf
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
```

#### 文件描述符限制
```bash
# /etc/security/limits.conf
datatrans soft nofile 65535
datatrans hard nofile 65535
```

### 2. 应用优化

#### 连接池配置
```python
# MongoDB 连接池
from pymongo import MongoClient

client = MongoClient(
    "mongodb://localhost:27017/",
    maxPoolSize=50,
    minPoolSize=10,
    maxIdleTimeMS=30000,
    waitQueueTimeoutMS=5000
)

# PostgreSQL 连接池
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    "postgresql://user:password@localhost:5432/datatrans",
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600
)
```

#### 异步优化
```python
# 使用异步连接池
import asyncio
import aioredis

async def get_redis_pool():
    return aioredis.ConnectionPool.from_url(
        "redis://localhost:6379",
        max_connections=20
    )
```

## 故障排除

### 常见问题

1. **调度器无法启动**
   - 检查端口占用：`netstat -tlnp | grep 8080`
   - 检查权限：确保用户有读写权限
   - 检查依赖：验证所有依赖服务正常运行

2. **任务执行失败**
   - 查看任务日志：`tail -f logs/workflow.log`
   - 检查资源使用：`top`, `free -h`, `df -h`
   - 验证网络连接：`ping`, `telnet`

3. **通知发送失败**
   - 检查网络连接
   - 验证配置信息
   - 测试SMTP连接：`telnet smtp.gmail.com 587`

### 调试工具

```bash
# 查看系统状态
systemctl status datatrans-workflow

# 查看实时日志
journalctl -u datatrans-workflow -f

# 检查进程
ps aux | grep python

# 检查网络连接
netstat -tlnp | grep python

# 检查磁盘使用
du -sh /opt/datatrans/*
```

## 升级指南

### 1. 准备工作
- 备份当前配置和数据
- 阅读版本更新说明
- 在测试环境验证升级

### 2. 升级步骤
```bash
# 停止服务
sudo systemctl stop datatrans-workflow

# 备份当前版本
cp -r /opt/datatrans /opt/datatrans.backup

# 更新代码
cd /opt/datatrans
git pull origin main

# 更新依赖
uv sync

# 运行迁移脚本（如果有）
uv run python scripts/migrate.py

# 重启服务
sudo systemctl start datatrans-workflow
```

### 3. 验证升级
- 检查服务状态
- 验证现有DAG正常运行
- 测试新功能

通过遵循本部署指南，您可以在各种环境中成功部署和运行 DataTrans 工作流系统。
