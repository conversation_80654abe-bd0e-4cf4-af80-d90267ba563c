{".class": "MypyFile", "_fullname": "tests.test_workflow", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseDAG": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.BaseDAG", "name": "BaseDAG", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.BaseDAG", "source_any": null, "type_of_any": 3}}}, "BaseOperator": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.BaseOperator", "name": "BaseOperator", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.BaseOperator", "source_any": null, "type_of_any": 3}}}, "CleanerOperator": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.CleanerOperator", "name": "CleanerOperator", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.CleanerOperator", "source_any": null, "type_of_any": 3}}}, "CrawlerOperator": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.CrawlerOperator", "name": "CrawlerOperator", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.CrawlerOperator", "source_any": null, "type_of_any": 3}}}, "DAGRunState": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.DAGRunState", "name": "DAGRunState", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.DAGRunState", "source_any": null, "type_of_any": 3}}}, "DataSourceSensor": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.DataSourceSensor", "name": "DataSourceSensor", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.DataSourceSensor", "source_any": null, "type_of_any": 3}}}, "Mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.Mock", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MockOperator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_workflow.MockOperator", "name": "MockOperator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "tests.test_workflow.MockOperator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_workflow", "mro": ["tests.test_workflow.MockOperator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "task_id", "dag", "return_value", "should_fail", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "tests.test_workflow.MockOperator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "task_id", "dag", "return_value", "should_fail", "kwargs"], "arg_types": ["tests.test_workflow.MockOperator", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MockOperator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.MockOperator.execute", "name": "execute", "type": null}}, "executed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tests.test_workflow.MockOperator.executed", "name": "executed", "setter_type": null, "type": "builtins.bool"}}, "return_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tests.test_workflow.MockOperator.return_value", "name": "return_value", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "should_fail": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tests.test_workflow.MockOperator.should_fail", "name": "should_fail", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_workflow.MockOperator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_workflow.MockOperator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotificationManager": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.NotificationManager", "name": "NotificationManager", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.NotificationManager", "source_any": null, "type_of_any": 3}}}, "QueueSensor": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.QueueSensor", "name": "QueueSensor", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.QueueSensor", "source_any": null, "type_of_any": 3}}}, "StorageOperator": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.StorageOperator", "name": "StorageOperator", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.StorageOperator", "source_any": null, "type_of_any": 3}}}, "TaskState": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.TaskState", "name": "TaskState", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.TaskState", "source_any": null, "type_of_any": 3}}}, "TestDAGTemplates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_workflow.TestDAGTemplates", "name": "TestDAGTemplates", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_workflow.TestDAGTemplates", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_workflow", "mro": ["tests.test_workflow.TestDAGTemplates", "builtins.object"], "names": {".class": "SymbolTable", "test_simple_crawl_dag_creation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestDAGTemplates.test_simple_crawl_dag_creation", "name": "test_simple_crawl_dag_creation", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_workflow.TestDAGTemplates.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_workflow.TestDAGTemplates", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestWorkflowBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_workflow.TestWorkflowBase", "name": "TestWorkflowBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_workflow.TestWorkflowBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_workflow", "mro": ["tests.test_workflow.TestWorkflowBase", "builtins.object"], "names": {".class": "SymbolTable", "test_dag_creation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowBase.test_dag_creation", "name": "test_dag_creation", "type": null}}, "test_dag_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowBase.test_dag_execution", "name": "test_dag_execution", "type": null}}, "test_dag_execution_with_failure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowBase.test_dag_execution_with_failure", "name": "test_dag_execution_with_failure", "type": null}}, "test_dag_validation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowBase.test_dag_validation", "name": "test_dag_validation", "type": null}}, "test_task_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowBase.test_task_dependencies", "name": "test_task_dependencies", "type": null}}, "test_xcom_data_exchange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowBase.test_xcom_data_exchange", "name": "test_xcom_data_exchange", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_workflow.TestWorkflowBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_workflow.TestWorkflowBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestWorkflowNotifications": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_workflow.TestWorkflowNotifications", "name": "TestWorkflowNotifications", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_workflow.TestWorkflowNotifications", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_workflow", "mro": ["tests.test_workflow.TestWorkflowNotifications", "builtins.object"], "names": {".class": "SymbolTable", "test_log_notifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowNotifications.test_log_notifier", "name": "test_log_notifier", "type": null}}, "test_notification_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowNotifications.test_notification_manager", "name": "test_notification_manager", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_workflow.TestWorkflowNotifications.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_workflow.TestWorkflowNotifications", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestWorkflowOperators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_workflow.TestWorkflowOperators", "name": "TestWorkflowOperators", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_workflow.TestWorkflowOperators", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_workflow", "mro": ["tests.test_workflow.TestWorkflowOperators", "builtins.object"], "names": {".class": "SymbolTable", "test_cleaner_operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_cleaner_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_workflow.TestWorkflowOperators.test_cleaner_operator", "name": "test_cleaner_operator", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.TestWorkflowOperators.test_cleaner_operator", "name": "test_cleaner_operator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_cleaner_operator of TestWorkflowOperators", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_crawler_operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_crawler_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_workflow.TestWorkflowOperators.test_crawler_operator", "name": "test_crawler_operator", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.TestWorkflowOperators.test_crawler_operator", "name": "test_crawler_operator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_crawler_operator of TestWorkflowOperators", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_workflow.TestWorkflowOperators.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_workflow.TestWorkflowOperators", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestWorkflowScheduler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_workflow.TestWorkflowScheduler", "name": "TestWorkflowScheduler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_workflow.TestWorkflowScheduler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_workflow", "mro": ["tests.test_workflow.TestWorkflowScheduler", "builtins.object"], "names": {".class": "SymbolTable", "test_scheduler_dag_registration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowScheduler.test_scheduler_dag_registration", "name": "test_scheduler_dag_registration", "type": null}}, "test_scheduler_dag_trigger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowScheduler.test_scheduler_dag_trigger", "name": "test_scheduler_dag_trigger", "type": null}}, "test_scheduler_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowScheduler.test_scheduler_status", "name": "test_scheduler_status", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_workflow.TestWorkflowScheduler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_workflow.TestWorkflowScheduler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestWorkflowSensors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_workflow.TestWorkflowSensors", "name": "TestWorkflowSensors", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_workflow.TestWorkflowSensors", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_workflow", "mro": ["tests.test_workflow.TestWorkflowSensors", "builtins.object"], "names": {".class": "SymbolTable", "test_http_sensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_client_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_workflow.TestWorkflowSensors.test_http_sensor", "name": "test_http_sensor", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.TestWorkflowSensors.test_http_sensor", "name": "test_http_sensor", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_http_sensor of TestWorkflowSensors", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_time_sensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.TestWorkflowSensors.test_time_sensor", "name": "test_time_sensor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_workflow.TestWorkflowSensors.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_workflow.TestWorkflowSensors", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeSensor": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.TimeSensor", "name": "TimeSensor", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.TimeSensor", "source_any": null, "type_of_any": 3}}}, "WorkflowScheduler": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.WorkflowScheduler", "name": "WorkflowScheduler", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.WorkflowScheduler", "source_any": null, "type_of_any": 3}}}, "XCom": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.XCom", "name": "XCom", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.XCom", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_workflow.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_workflow.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_workflow.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_workflow.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_workflow.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_workflow.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "create_simple_crawl_dag": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.create_simple_crawl_dag", "name": "create_simple_crawl_dag", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.create_simple_crawl_dag", "source_any": null, "type_of_any": 3}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pytest": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_workflow.pytest", "name": "pytest", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_workflow.pytest", "source_any": null, "type_of_any": 3}}}, "run_workflow_tests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_workflow.run_workflow_tests", "name": "run_workflow_tests", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "tests\\test_workflow.py"}