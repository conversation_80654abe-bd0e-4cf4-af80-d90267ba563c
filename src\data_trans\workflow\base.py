"""
工作流基础类 - 模拟Airflow的核心概念
"""

import asyncio
import logging
import uuid
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

logger = logging.getLogger(__name__)


class TaskState(Enum):
    """任务状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRY = "retry"


class XCom:
    """模拟Airflow的XCom数据交换"""

    _data: Dict[str, Any] = {}

    @classmethod
    def push(cls, key: str, value: Any, task_id: str, dag_id: str):
        """推送数据"""
        xcom_key = f"{dag_id}.{task_id}.{key}"
        cls._data[xcom_key] = value
        logger.info(f"XCom推送数据: {xcom_key} = {value}")

    @classmethod
    def pull(cls, key: str, task_id: str, dag_id: str) -> Any:
        """拉取数据"""
        xcom_key = f"{dag_id}.{task_id}.{key}"
        value = cls._data.get(xcom_key)
        logger.info(f"XCom拉取数据: {xcom_key} = {value}")
        return value

    @classmethod
    def clear(cls, dag_id: str):
        """清理DAG的所有XCom数据"""
        keys_to_remove = [k for k in cls._data.keys() if k.startswith(f"{dag_id}.")]
        for key in keys_to_remove:
            del cls._data[key]


class TaskInstance:
    """任务实例"""

    def __init__(self, task_id: str, dag_id: str, execution_date: datetime):
        self.task_id = task_id
        self.dag_id = dag_id
        self.execution_date = execution_date
        self.state = TaskState.PENDING
        self.start_date: Optional[datetime] = None
        self.end_date: Optional[datetime] = None
        self.log: List[str] = []
        self.retry_count = 0
        self.max_retries = 3

    def log_info(self, message: str):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log.append(log_entry)
        logger.info(f"[{self.dag_id}.{self.task_id}] {message}")


class BaseOperator(ABC):
    """基础操作器"""

    def __init__(
        self,
        task_id: str,
        dag: "BaseDAG",
        depends_on_past: bool = False,
        retries: int = 3,
        retry_delay: timedelta = timedelta(minutes=5),
        **kwargs,
    ):
        self.task_id = task_id
        self.dag = dag
        self.depends_on_past = depends_on_past
        self.retries = retries
        self.retry_delay = retry_delay
        self.upstream_task_ids: List[str] = []
        self.downstream_task_ids: List[str] = []

        # 将任务添加到DAG
        dag.add_task(self)

    @abstractmethod
    async def execute(self, context: Dict[str, Any]) -> Any:
        """执行任务的抽象方法"""
        pass

    def set_upstream(self, task: "BaseOperator"):
        """设置上游任务"""
        if task.task_id not in self.upstream_task_ids:
            self.upstream_task_ids.append(task.task_id)
        if self.task_id not in task.downstream_task_ids:
            task.downstream_task_ids.append(self.task_id)

    def set_downstream(self, task: "BaseOperator"):
        """设置下游任务"""
        task.set_upstream(self)

    def __rshift__(self, other: "BaseOperator"):
        """支持 >> 操作符"""
        self.set_downstream(other)
        return other

    def __lshift__(self, other: "BaseOperator"):
        """支持 << 操作符"""
        self.set_upstream(other)
        return self


class BaseSensor(BaseOperator):
    """基础传感器"""

    def __init__(
        self,
        task_id: str,
        dag: "BaseDAG",
        poke_interval: int = 60,
        timeout: int = 3600,
        **kwargs,
    ):
        super().__init__(task_id, dag, **kwargs)
        self.poke_interval = poke_interval
        self.timeout = timeout

    @abstractmethod
    async def poke(self, context: Dict[str, Any]) -> bool:
        """检查条件是否满足"""
        pass

    async def execute(self, context: Dict[str, Any]) -> Any:
        """执行传感器逻辑"""
        start_time = datetime.now()
        timeout_time = start_time + timedelta(seconds=self.timeout)

        while datetime.now() < timeout_time:
            if await self.poke(context):
                return True

            await asyncio.sleep(self.poke_interval)

        raise TimeoutError(f"传感器 {self.task_id} 超时")


class BaseDAG:
    """基础DAG"""

    def __init__(
        self,
        dag_id: str,
        description: str = "",
        schedule_interval: Optional[str] = None,
        start_date: Optional[datetime] = None,
        catchup: bool = False,
        max_active_runs: int = 1,
        default_args: Optional[Dict[str, Any]] = None,
        **kwargs,
    ):
        self.dag_id = dag_id
        self.description = description
        self.schedule_interval = schedule_interval
        self.start_date = start_date or datetime.now()
        self.catchup = catchup
        self.max_active_runs = max_active_runs
        self.default_args = default_args or {}

        self.tasks: Dict[str, BaseOperator] = {}
        self.task_instances: List[TaskInstance] = []

        # 回调函数
        self.on_success_callback: Optional[Callable] = kwargs.get("on_success_callback")
        self.on_failure_callback: Optional[Callable] = kwargs.get("on_failure_callback")

    def add_task(self, task: BaseOperator):
        """添加任务到DAG"""
        self.tasks[task.task_id] = task

    def get_task(self, task_id: str) -> BaseOperator:
        """获取任务"""
        return self.tasks[task_id]

    def get_task_dependencies(self) -> Dict[str, List[str]]:
        """获取任务依赖关系"""
        dependencies = {}
        for task_id, task in self.tasks.items():
            dependencies[task_id] = task.upstream_task_ids
        return dependencies

    def validate(self) -> bool:
        """验证DAG的有效性"""
        # 检查循环依赖
        visited = set()
        rec_stack = set()

        def has_cycle(task_id: str) -> bool:
            visited.add(task_id)
            rec_stack.add(task_id)

            task = self.tasks.get(task_id)
            if task:
                for downstream_id in task.downstream_task_ids:
                    if downstream_id not in visited:
                        if has_cycle(downstream_id):
                            return True
                    elif downstream_id in rec_stack:
                        return True

            rec_stack.remove(task_id)
            return False

        for task_id in self.tasks:
            if task_id not in visited:
                if has_cycle(task_id):
                    logger.error(f"DAG {self.dag_id} 存在循环依赖")
                    return False

        return True

    async def run(self, execution_date: Optional[datetime] = None) -> bool:
        """运行DAG"""
        if not self.validate():
            logger.error(f"DAG {self.dag_id} 验证失败")
            return False

        execution_date = execution_date or datetime.now()
        logger.info(f"开始运行DAG: {self.dag_id}, 执行时间: {execution_date}")

        # 清理之前的XCom数据
        XCom.clear(self.dag_id)

        # 创建任务实例
        task_instances = {}
        for task_id in self.tasks:
            ti = TaskInstance(task_id, self.dag_id, execution_date)
            task_instances[task_id] = ti
            self.task_instances.append(ti)

        # 按拓扑顺序执行任务
        completed_tasks = set()
        failed_tasks = set()

        while len(completed_tasks) + len(failed_tasks) < len(self.tasks):
            # 找到可以执行的任务（所有依赖都已完成）
            ready_tasks = []
            for task_id, task in self.tasks.items():
                if (
                    task_id not in completed_tasks
                    and task_id not in failed_tasks
                    and all(dep in completed_tasks for dep in task.upstream_task_ids)
                ):
                    ready_tasks.append(task_id)

            if not ready_tasks:
                logger.error(f"DAG {self.dag_id} 无法继续执行，可能存在依赖问题")
                break

            # 并行执行准备好的任务
            tasks_to_run = []
            for task_id in ready_tasks:
                task = self.tasks[task_id]
                ti = task_instances[task_id]
                tasks_to_run.append(self._run_task(task, ti))

            # 等待所有任务完成
            results = await asyncio.gather(*tasks_to_run, return_exceptions=True)

            # 处理结果
            for i, task_id in enumerate(ready_tasks):
                ti = task_instances[task_id]
                if isinstance(results[i], Exception):
                    ti.state = TaskState.FAILED
                    failed_tasks.add(task_id)
                    logger.error(f"任务 {task_id} 执行失败: {results[i]}")
                else:
                    ti.state = TaskState.SUCCESS
                    completed_tasks.add(task_id)
                    logger.info(f"任务 {task_id} 执行成功")

        # 检查执行结果
        success = len(failed_tasks) == 0

        if success and self.on_success_callback:
            try:
                await self.on_success_callback(self, execution_date)
            except Exception as e:
                logger.error(f"成功回调执行失败: {e}")

        if not success and self.on_failure_callback:
            try:
                await self.on_failure_callback(self, execution_date)
            except Exception as e:
                logger.error(f"失败回调执行失败: {e}")

        logger.info(f"DAG {self.dag_id} 执行完成，成功: {success}")
        return success

    async def _run_task(self, task: BaseOperator, ti: TaskInstance) -> Any:
        """运行单个任务"""
        ti.start_date = datetime.now()
        ti.state = TaskState.RUNNING
        ti.log_info(f"开始执行任务")

        context = {
            "dag": self,
            "task": task,
            "task_instance": ti,
            "execution_date": ti.execution_date,
            "dag_run": None,  # 简化实现
        }

        try:
            result = await task.execute(context)
            ti.end_date = datetime.now()
            ti.log_info(f"任务执行成功")
            return result
        except Exception as e:
            ti.end_date = datetime.now()
            ti.log_info(f"任务执行失败: {str(e)}")
            raise e
