# DataTrans 工作流系统

## 🚀 概述

DataTrans 工作流系统是一个轻量级但功能完整的工作流管理平台，专为数据采集、清洗和存储任务设计。由于 Apache Airflow 在 Windows 环境下的兼容性问题，我们开发了这个类似 Airflow 的工作流系统，提供了完整的 DAG 管理、任务调度、监控和通知功能。

## ✨ 核心特性

- 🔄 **DAG 管理**：支持复杂的有向无环图任务依赖关系
- 🛠️ **丰富的操作器**：内置爬虫、清洗、存储等专用操作器
- 📡 **智能传感器**：监控数据源、队列、时间等多种条件
- ⏰ **灵活调度**：支持定时调度和手动触发
- 📢 **多渠道通知**：支持日志、邮件、Slack、钉钉等通知方式
- ⚙️ **配置管理**：统一的连接和变量管理
- 📊 **实时监控**：任务执行状态和日志实时跟踪
- 🎯 **模板系统**：预定义的 DAG 模板快速开始

## 🏗️ 系统架构

```
DataTrans 工作流系统
├── 🧠 核心引擎
│   ├── BaseDAG - DAG 基础类
│   ├── BaseOperator - 操作器基础类
│   ├── BaseSensor - 传感器基础类
│   └── XCom - 数据交换机制
├── 🔧 操作器集合
│   ├── CrawlerOperator - 网页爬取
│   ├── CleanerOperator - 数据清洗
│   ├── StorageOperator - 数据存储
│   └── NotificationOperator - 消息通知
├── 📡 传感器系统
│   ├── TimeSensor - 时间触发
│   ├── DataSourceSensor - 数据源监控
│   ├── QueueSensor - 队列监控
│   └── HttpSensor - HTTP 端点监控
├── 🎛️ 调度引擎
│   └── WorkflowScheduler - 统一调度管理
├── 📢 通知中心
│   ├── LogNotifier - 日志通知
│   ├── EmailNotifier - 邮件通知
│   ├── SlackNotifier - Slack 通知
│   └── DingTalkNotifier - 钉钉通知
└── ⚙️ 配置中心
    ├── ConnectionManager - 连接管理
    ├── VariableManager - 变量管理
    └── ConfigManager - 统一配置
```

## 🚀 快速开始

### 安装

```bash
# 使用 uv（推荐）
uv add apache-airflow

# 或使用 pip
pip install -r requirements.txt
```

### 基础示例

```python
import asyncio
from datetime import datetime
from data_trans.workflow.base import BaseDAG, BaseOperator
from data_trans.workflow.scheduler import get_scheduler

# 创建自定义操作器
class HelloOperator(BaseOperator):
    def __init__(self, task_id: str, dag, message: str, **kwargs):
        super().__init__(task_id, dag, **kwargs)
        self.message = message

    async def execute(self, context):
        print(f"[{self.task_id}] {self.message}")
        return f"完成: {self.message}"

# 创建 DAG
dag = BaseDAG(
    dag_id="hello_world",
    description="Hello World 工作流",
    start_date=datetime.now()
)

# 创建任务
task1 = HelloOperator("say_hello", dag, "Hello")
task2 = HelloOperator("say_world", dag, "World")

# 设置依赖关系
task1 >> task2

# 运行工作流
async def main():
    scheduler = get_scheduler()
    scheduler.register_dag(dag)
    run_id = await scheduler.trigger_dag("hello_world")
    print(f"工作流已启动，运行ID: {run_id}")

asyncio.run(main())
```

### 使用模板快速创建

```python
from data_trans.workflow.dag_templates import create_simple_crawl_dag

# 创建爬取工作流
dag = create_simple_crawl_dag(
    dag_id="news_crawler",
    target_urls=["https://news.example.com"],
    crawler_config={"timeout": 30, "user_agent": "NewsBot/1.0"},
    cleaner_config={"remove_html": True, "extract_text": True},
    storage_config={"type": "mongodb", "collection": "news_data"}
)
```

## 📚 文档

- 📖 [完整文档](workflow_system.md) - 详细的使用指南和概念说明
- 🔧 [API 参考](workflow_api_reference.md) - 完整的 API 文档
- 🚀 [部署指南](workflow_deployment_guide.md) - 生产环境部署指南

## 🎯 使用场景

### 1. 数据采集流水线
```python
# 新闻采集 -> 内容清洗 -> 数据存储 -> 结果通知
news_crawler >> content_cleaner >> data_storage >> notification
```

### 2. 批量数据处理
```python
# 多源并行采集 -> 数据合并 -> 批量处理 -> 分发存储
[source1_crawler, source2_crawler, source3_crawler] >> data_merger >> batch_processor >> data_distributor
```

### 3. 实时监控工作流
```python
# 数据源监控 -> 触发采集 -> 实时清洗 -> 即时存储
data_sensor >> realtime_crawler >> stream_cleaner >> instant_storage
```

### 4. 定时任务调度
```python
# 时间触发 -> 定期采集 -> 增量更新 -> 状态报告
time_sensor >> scheduled_crawler >> incremental_update >> status_report
```

## 🔧 核心组件

### DAG（有向无环图）
- 定义任务间的依赖关系
- 支持复杂的工作流编排
- 自动检测循环依赖

### 操作器（Operators）
- **CrawlerOperator**: 网页数据采集
- **CleanerOperator**: 数据清洗和转换
- **StorageOperator**: 多种存储后端支持
- **NotificationOperator**: 多渠道消息通知

### 传感器（Sensors）
- **TimeSensor**: 基于时间的触发条件
- **DataSourceSensor**: 监控文件、数据库、API 变化
- **QueueSensor**: Redis 队列状态监控
- **HttpSensor**: HTTP 端点健康检查

### 调度器（Scheduler）
- 自动任务调度
- 手动触发支持
- 执行状态跟踪
- 失败重试机制

## 🎨 DAG 模板

### 简单爬取模板
```python
create_simple_crawl_dag(
    dag_id="simple_crawl",
    target_urls=["https://example.com"],
    schedule_interval="@daily"
)
```

### 批量处理模板
```python
create_batch_process_dag(
    dag_id="batch_process",
    url_batches=[batch1, batch2, batch3],
    schedule_interval="0 2 * * *"
)
```

### 实时处理模板
```python
create_realtime_dag(
    dag_id="realtime_process",
    data_source_config={"type": "queue", "queue_name": "data_queue"}
)
```

### 定时调度模板
```python
create_scheduled_dag(
    dag_id="scheduled_task",
    schedule_time="09:00",
    target_urls=["https://api.example.com"]
)
```

## 📊 监控和通知

### 多渠道通知支持
- 📝 **日志通知**: 结构化日志记录
- 📧 **邮件通知**: SMTP 邮件发送
- 💬 **Slack 通知**: Webhook 集成
- 📱 **钉钉通知**: 企业级即时通知
- 🔗 **Webhook 通知**: 自定义 HTTP 回调

### 实时状态监控
- 任务执行状态跟踪
- 详细的执行日志
- 性能指标收集
- 错误告警机制

## ⚙️ 配置管理

### 连接管理
```python
# 数据库连接
config_manager.add_connection(
    conn_id="my_postgres",
    conn_type="postgres",
    host="localhost",
    port=5432,
    schema="mydb"
)

# API 连接
config_manager.add_connection(
    conn_id="external_api",
    conn_type="http",
    host="api.example.com",
    port=443
)
```

### 变量管理
```python
# 普通变量
config_manager.set_variable("timeout", 30)

# 加密变量
config_manager.set_variable(
    "api_key",
    "secret_value",
    is_encrypted=True
)
```

## 🧪 测试

运行测试套件：
```bash
# 运行完整测试
uv run python tests/test_workflow.py

# 运行最小化测试
uv run python examples/minimal_workflow_test.py
```

## 🔒 安全特性

- 🔐 **敏感信息加密**: 支持变量加密存储
- 🛡️ **访问控制**: 基于角色的权限管理
- 🔍 **审计日志**: 完整的操作记录
- 🌐 **网络安全**: SSL/TLS 支持

## 🚀 性能特性

- ⚡ **异步执行**: 基于 asyncio 的高性能异步处理
- 🔄 **并行处理**: 支持任务并行执行
- 📈 **可扩展性**: 水平扩展支持
- 💾 **资源优化**: 智能资源管理和回收

## 🛠️ 开发和扩展

### 自定义操作器
```python
class CustomOperator(BaseOperator):
    async def execute(self, context):
        # 实现自定义逻辑
        return "自定义结果"
```

### 自定义传感器
```python
class CustomSensor(BaseSensor):
    async def poke(self, context):
        # 实现检查逻辑
        return True  # 条件满足时返回 True
```

## 📋 系统要求

- **Python**: 3.11+
- **内存**: 2GB+ (推荐 8GB+)
- **存储**: 10GB+ (推荐 50GB+)
- **网络**: 稳定的互联网连接

### 可选依赖
- **MongoDB**: 文档数据存储
- **PostgreSQL**: 关系型数据存储
- **Redis**: 队列和缓存
- **SMTP 服务**: 邮件通知

## 🤝 贡献

我们欢迎社区贡献！请查看贡献指南了解如何参与项目开发。

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 🆘 支持

- 📖 查看[文档](workflow_system.md)获取详细信息
- 🐛 在 GitHub Issues 中报告问题
- 💬 在讨论区交流使用经验

---

**DataTrans 工作流系统** - 让数据处理工作流变得简单而强大！ 🚀
