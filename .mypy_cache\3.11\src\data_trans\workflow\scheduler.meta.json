{"data_mtime": 1752408380, "dep_lines": [14, 15, 5, 6, 7, 8, 9, 10, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 17, 18], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["src.data_trans.workflow.base", "src.data_trans.workflow.notifications", "asyncio", "logging", "uuid", "dataclasses", "datetime", "enum", "typing", "builtins", "_asyncio", "_collections_abc", "_contextvars", "_frozen_importlib", "abc", "asyncio.exceptions", "types", "typing_extensions"], "hash": "07ca79b410280c1eb1cb6497072d8ac0a06b5139", "id": "src.data_trans.workflow.scheduler", "ignore_all": false, "interface_hash": "2c24223674caf6f0bb268ac3b859988dfecf0ee5", "mtime": 1752408378, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src\\data_trans\\workflow\\scheduler.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 11170, "suppressed": ["base", "notifications"], "version_id": "1.16.1"}