"""
工作流系统使用示例
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from data_trans.workflow.config_manager import get_config_manager
from data_trans.workflow.dag_templates import (
    create_batch_process_dag,
    create_realtime_dag,
    create_scheduled_dag,
    create_simple_crawl_dag,
)
from data_trans.workflow.notifications import get_notification_manager
from data_trans.workflow.scheduler import get_scheduler

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)


async def setup_workflow_environment():
    """设置工作流环境"""
    logger.info("设置工作流环境...")

    # 获取配置管理器
    config_manager = get_config_manager()

    # 设置默认配置
    config_manager.setup_default_configs()

    # 设置通知配置
    notification_config = {
        "log": {"enabled": True},
        "email": {
            "enabled": False,  # 示例中禁用邮件通知
            "smtp_host": "localhost",
            "smtp_port": 587,
            "from_email": "<EMAIL>",
            "recipients": ["<EMAIL>"],
        },
    }

    # 获取通知管理器
    notification_manager = get_notification_manager(notification_config)

    logger.info("工作流环境设置完成")
    return config_manager, notification_manager


async def demo_simple_crawl_dag():
    """演示简单爬取DAG"""
    logger.info("=== 演示简单爬取DAG ===")

    # 创建简单爬取DAG
    dag = create_simple_crawl_dag(
        dag_id="demo_simple_crawl",
        target_urls=[
            "https://httpbin.org/json",
            "https://httpbin.org/uuid",
            "https://httpbin.org/ip",
        ],
        crawler_config={
            "timeout": 30,
            "max_retries": 2,
            "delay": 1,
            "user_agent": "DataTrans-Demo/1.0",
        },
        cleaner_config={
            "remove_html": True,
            "normalize_whitespace": True,
            "extract_json": True,
        },
        storage_config={"type": "mongodb", "collection": "demo_crawl_data"},
    )

    # 获取调度器
    scheduler = get_scheduler()
    scheduler.register_dag(dag)

    # 手动触发DAG
    run_id = await scheduler.trigger_dag("demo_simple_crawl")
    logger.info(f"触发简单爬取DAG，运行ID: {run_id}")

    # 等待执行完成
    await asyncio.sleep(5)

    # 检查执行结果
    dag_run = scheduler.get_dag_run(run_id)
    if dag_run:
        logger.info(f"DAG执行状态: {dag_run.state}")
        logger.info(f"执行日志: {dag_run.log_messages[-3:]}")  # 显示最后3条日志

    return dag_run


async def demo_batch_process_dag():
    """演示批量处理DAG"""
    logger.info("=== 演示批量处理DAG ===")

    # 创建批量处理DAG
    url_batches = [
        ["https://httpbin.org/json", "https://httpbin.org/uuid"],
        ["https://httpbin.org/ip", "https://httpbin.org/user-agent"],
        ["https://httpbin.org/headers", "https://httpbin.org/get"],
    ]

    dag = create_batch_process_dag(
        dag_id="demo_batch_process",
        url_batches=url_batches,
        crawler_config={"timeout": 30, "concurrent_requests": 3, "delay": 0.5},
        cleaner_config={"batch_size": 50, "parallel_processing": True},
        storage_config={"type": "mongodb", "collection": "demo_batch_data"},
    )

    # 获取调度器
    scheduler = get_scheduler()
    scheduler.register_dag(dag)

    # 手动触发DAG
    run_id = await scheduler.trigger_dag("demo_batch_process")
    logger.info(f"触发批量处理DAG，运行ID: {run_id}")

    # 等待执行完成
    await asyncio.sleep(8)

    # 检查执行结果
    dag_run = scheduler.get_dag_run(run_id)
    if dag_run:
        logger.info(f"DAG执行状态: {dag_run.state}")
        logger.info(f"执行日志: {dag_run.log_messages[-3:]}")

    return dag_run


async def demo_scheduled_dag():
    """演示定时调度DAG"""
    logger.info("=== 演示定时调度DAG ===")

    # 创建定时调度DAG（设置为当前时间后1分钟）
    target_time = (datetime.now().replace(second=0, microsecond=0)).strftime("%H:%M")

    dag = create_scheduled_dag(
        dag_id="demo_scheduled",
        schedule_time=target_time,
        target_urls=["https://httpbin.org/json"],
        crawler_config={"timeout": 30},
        cleaner_config={"remove_html": True},
        storage_config={"type": "mongodb", "collection": "demo_scheduled_data"},
    )

    # 获取调度器
    scheduler = get_scheduler()
    scheduler.register_dag(dag)

    logger.info(f"注册定时DAG，目标时间: {target_time}")
    logger.info("定时DAG已注册，将在指定时间自动执行")

    return dag


async def demo_realtime_dag():
    """演示实时处理DAG"""
    logger.info("=== 演示实时处理DAG ===")

    # 创建实时处理DAG
    data_source_config = {
        "type": "api",
        "url": "https://httpbin.org/json",
        "check_field": "slideshow",
        "urls": ["https://httpbin.org/json", "https://httpbin.org/uuid"],
    }

    dag = create_realtime_dag(
        dag_id="demo_realtime",
        data_source_config=data_source_config,
        crawler_config={"timeout": 15, "realtime_mode": True},
        cleaner_config={"realtime_processing": True},
        storage_config={"type": "mongodb", "collection": "demo_realtime_data"},
        sensor_config={"check_interval": 30, "timeout": 300},
    )

    # 获取调度器
    scheduler = get_scheduler()
    scheduler.register_dag(dag)

    logger.info("注册实时处理DAG，传感器将监控数据源变化")

    return dag


async def demo_scheduler_management():
    """演示调度器管理"""
    logger.info("=== 演示调度器管理 ===")

    scheduler = get_scheduler()

    # 显示调度器状态
    status = scheduler.get_scheduler_status()
    logger.info(f"调度器状态: {status}")

    # 列出所有DAG
    dags = scheduler.list_dags()
    logger.info(f"已注册的DAG数量: {len(dags)}")
    for dag in dags:
        logger.info(f"  - {dag.dag_id}: {dag.description}")

    # 列出DAG运行记录
    dag_runs = scheduler.list_dag_runs()
    logger.info(f"DAG运行记录数量: {len(dag_runs)}")
    for run in dag_runs[-5:]:  # 显示最近5次运行
        logger.info(
            f"  - {run.dag_id} ({run.run_id}): {run.state} at {run.execution_date}"
        )


async def demo_configuration_management():
    """演示配置管理"""
    logger.info("=== 演示配置管理 ===")

    config_manager = get_config_manager()

    # 显示配置摘要
    summary = config_manager.get_config_summary()
    logger.info(f"配置摘要: {summary}")

    # 列出连接
    connections = config_manager.list_connections()
    logger.info(f"已配置的连接数量: {len(connections)}")
    for conn in connections:
        logger.info(f"  - {conn.conn_id} ({conn.conn_type}): {conn.description}")

    # 列出变量
    variables = config_manager.list_variables()
    logger.info(f"已配置的变量数量: {len(variables)}")
    for var in variables:
        logger.info(f"  - {var.key}: {var.description}")

    # 验证配置
    validation_results = config_manager.validate_configs()
    logger.info(f"配置验证结果: {validation_results}")


async def main():
    """主函数"""
    logger.info("开始工作流系统演示...")

    try:
        # 设置环境
        config_manager, notification_manager = await setup_workflow_environment()

        # 演示配置管理
        await demo_configuration_management()

        # 演示简单爬取DAG
        await demo_simple_crawl_dag()

        # 演示批量处理DAG
        await demo_batch_process_dag()

        # 演示定时调度DAG
        await demo_scheduled_dag()

        # 演示实时处理DAG
        await demo_realtime_dag()

        # 演示调度器管理
        await demo_scheduler_management()

        # 发送演示完成通知
        await notification_manager.send_notification(
            message="工作流系统演示已完成，所有功能运行正常",
            subject="工作流演示完成",
            channels=["log"],
        )

        logger.info("工作流系统演示完成！")

    except Exception as e:
        logger.error(f"演示过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
