# 工作流系统 API 参考

## 核心类

### BaseDAG

DAG（有向无环图）的基础类。

```python
class BaseDAG:
    def __init__(
        self,
        dag_id: str,
        description: str = "",
        schedule_interval: Optional[str] = None,
        start_date: Optional[datetime] = None,
        catchup: bool = False,
        max_active_runs: int = 1,
        default_args: Optional[Dict[str, Any]] = None,
        **kwargs
    )
```

**参数：**
- `dag_id`: DAG的唯一标识符
- `description`: DAG描述
- `schedule_interval`: 调度间隔（如 "@daily", "@hourly"）
- `start_date`: 开始日期
- `catchup`: 是否执行历史任务
- `max_active_runs`: 最大并行运行数
- `default_args`: 默认参数

**方法：**
- `add_task(task: BaseOperator)`: 添加任务
- `get_task(task_id: str) -> BaseOperator`: 获取任务
- `validate() -> bool`: 验证DAG有效性
- `run(execution_date: Optional[datetime] = None) -> bool`: 运行DAG

### BaseOperator

操作器的基础类。

```python
class BaseOperator:
    def __init__(
        self,
        task_id: str,
        dag: BaseDAG,
        depends_on_past: bool = False,
        retries: int = 3,
        retry_delay: timedelta = timedelta(minutes=5),
        **kwargs
    )
```

**参数：**
- `task_id`: 任务唯一标识符
- `dag`: 所属DAG
- `depends_on_past`: 是否依赖过去的执行
- `retries`: 重试次数
- `retry_delay`: 重试延迟

**方法：**
- `execute(context: Dict[str, Any]) -> Any`: 执行任务（抽象方法）
- `set_upstream(task: BaseOperator)`: 设置上游任务
- `set_downstream(task: BaseOperator)`: 设置下游任务

**操作符重载：**
- `>>`: 设置下游任务
- `<<`: 设置上游任务

### BaseSensor

传感器的基础类。

```python
class BaseSensor(BaseOperator):
    def __init__(
        self,
        task_id: str,
        dag: BaseDAG,
        poke_interval: int = 60,
        timeout: int = 3600,
        **kwargs
    )
```

**参数：**
- `poke_interval`: 检查间隔（秒）
- `timeout`: 超时时间（秒）

**方法：**
- `poke(context: Dict[str, Any]) -> bool`: 检查条件（抽象方法）

## 操作器类

### CrawlerOperator

爬虫操作器，用于执行网页爬取任务。

```python
class CrawlerOperator(BaseOperator):
    def __init__(
        self,
        task_id: str,
        dag: BaseDAG,
        crawler_config: Dict[str, Any],
        target_urls: Optional[List[str]] = None,
        output_key: str = "crawl_result",
        **kwargs
    )
```

**参数：**
- `crawler_config`: 爬虫配置
- `target_urls`: 目标URL列表
- `output_key`: 输出数据的XCom键

**配置示例：**
```python
crawler_config = {
    "timeout": 30,
    "max_retries": 3,
    "delay": 1,
    "user_agent": "DataTrans/1.0",
    "headers": {"Accept": "application/json"}
}
```

### CleanerOperator

数据清洗操作器。

```python
class CleanerOperator(BaseOperator):
    def __init__(
        self,
        task_id: str,
        dag: BaseDAG,
        cleaner_config: Dict[str, Any],
        input_key: str = "crawl_result",
        output_key: str = "clean_result",
        **kwargs
    )
```

**参数：**
- `cleaner_config`: 清洗配置
- `input_key`: 输入数据的XCom键
- `output_key`: 输出数据的XCom键

**配置示例：**
```python
cleaner_config = {
    "remove_html": True,
    "normalize_whitespace": True,
    "remove_empty": True,
    "extract_json": True
}
```

### StorageOperator

数据存储操作器。

```python
class StorageOperator(BaseOperator):
    def __init__(
        self,
        task_id: str,
        dag: BaseDAG,
        storage_config: Dict[str, Any],
        input_key: str = "clean_result",
        storage_type: str = "mongodb",
        collection_name: Optional[str] = None,
        **kwargs
    )
```

**参数：**
- `storage_config`: 存储配置
- `input_key`: 输入数据的XCom键
- `storage_type`: 存储类型（mongodb, postgresql）
- `collection_name`: 集合/表名

### NotificationOperator

通知操作器。

```python
class NotificationOperator(BaseOperator):
    def __init__(
        self,
        task_id: str,
        dag: BaseDAG,
        notification_config: Dict[str, Any],
        message_template: str = "DAG {dag_id} 执行完成",
        **kwargs
    )
```

## 传感器类

### TimeSensor

时间传感器，基于时间条件触发。

```python
class TimeSensor(BaseSensor):
    def __init__(
        self,
        task_id: str,
        dag: BaseDAG,
        target_time: str,  # "HH:MM" 或 "YYYY-MM-DD HH:MM:SS"
        check_interval: int = 60,
        **kwargs
    )
```

### DataSourceSensor

数据源传感器，监控数据源变化。

```python
class DataSourceSensor(BaseSensor):
    def __init__(
        self,
        task_id: str,
        dag: BaseDAG,
        source_type: str,  # "file", "database", "api"
        source_config: Dict[str, Any],
        check_interval: int = 60,
        **kwargs
    )
```

### QueueSensor

队列传感器，监控Redis队列状态。

```python
class QueueSensor(BaseSensor):
    def __init__(
        self,
        task_id: str,
        dag: BaseDAG,
        queue_name: str,
        min_messages: int = 1,
        max_messages: Optional[int] = None,
        check_interval: int = 30,
        **kwargs
    )
```

### HttpSensor

HTTP传感器，监控HTTP端点状态。

```python
class HttpSensor(BaseSensor):
    def __init__(
        self,
        task_id: str,
        dag: BaseDAG,
        endpoint: str,
        method: str = "GET",
        expected_status: int = 200,
        headers: Optional[Dict[str, str]] = None,
        check_interval: int = 60,
        **kwargs
    )
```

## 调度器

### WorkflowScheduler

工作流调度器，管理DAG的执行和调度。

```python
class WorkflowScheduler:
    def __init__(self, notification_config: Optional[Dict] = None)
```

**方法：**
- `register_dag(dag: BaseDAG)`: 注册DAG
- `unregister_dag(dag_id: str)`: 注销DAG
- `trigger_dag(dag_id: str, execution_date: Optional[datetime] = None, run_id: Optional[str] = None) -> Optional[str]`: 触发DAG执行
- `cancel_dag_run(run_id: str) -> bool`: 取消DAG运行
- `get_dag_run(run_id: str) -> Optional[DAGRun]`: 获取DAG运行实例
- `list_dag_runs(dag_id: Optional[str] = None, state: Optional[DAGRunState] = None) -> List[DAGRun]`: 列出DAG运行实例
- `start_scheduler(check_interval: int = 60)`: 启动调度器
- `stop_scheduler()`: 停止调度器
- `get_scheduler_status() -> Dict`: 获取调度器状态

## 通知系统

### NotificationManager

通知管理器，支持多种通知渠道。

```python
class NotificationManager:
    def __init__(self, config: Optional[Dict[str, Any]] = None)
```

**方法：**
- `send_notification(message: str, subject: str = "", channels: Optional[List[str]] = None, level: str = "info", **kwargs) -> Dict[str, bool]`: 发送通知
- `send_success_notification(dag_id: str, execution_date: str, **kwargs) -> Dict[str, bool]`: 发送成功通知
- `send_failure_notification(dag_id: str, execution_date: str, error: str, **kwargs) -> Dict[str, bool]`: 发送失败通知
- `send_retry_notification(dag_id: str, task_id: str, retry_count: int, **kwargs) -> Dict[str, bool]`: 发送重试通知

### 通知器类

#### LogNotifier
```python
class LogNotifier(BaseNotifier):
    async def send(self, message: str, subject: str = "", **kwargs) -> bool
```

#### EmailNotifier
```python
class EmailNotifier(BaseNotifier):
    def __init__(self, config: Dict[str, Any])
    # config包含: smtp_host, smtp_port, smtp_user, smtp_password, from_email, recipients, use_tls
```

#### SlackNotifier
```python
class SlackNotifier(BaseNotifier):
    def __init__(self, config: Dict[str, Any])
    # config包含: webhook_url, channel, username, icon_emoji
```

#### DingTalkNotifier
```python
class DingTalkNotifier(BaseNotifier):
    def __init__(self, config: Dict[str, Any])
    # config包含: webhook_url, secret
```

## 配置管理

### ConfigManager

统一配置管理器。

```python
class ConfigManager:
    def __init__(self)
```

**连接管理方法：**
- `add_connection(conn_id: str, conn_type: str, host: str = None, port: int = None, schema: str = None, login: str = None, password: str = None, extra: Dict = None, description: str = None) -> bool`: 添加连接
- `get_connection(conn_id: str) -> Optional[Connection]`: 获取连接
- `get_connection_uri(conn_id: str) -> Optional[str]`: 获取连接URI
- `test_connection(conn_id: str) -> bool`: 测试连接
- `list_connections() -> List[Connection]`: 列出所有连接
- `update_connection(conn_id: str, **kwargs) -> bool`: 更新连接
- `delete_connection(conn_id: str) -> bool`: 删除连接

**变量管理方法：**
- `set_variable(key: str, value: Any, description: str = None, is_encrypted: bool = False) -> bool`: 设置变量
- `get_variable(key: str, default_value: Any = None) -> Any`: 获取变量
- `get_variable_info(key: str) -> Optional[Variable]`: 获取变量信息
- `list_variables() -> List[Variable]`: 列出所有变量
- `update_variable(key: str, **kwargs) -> bool`: 更新变量
- `delete_variable(key: str) -> bool`: 删除变量

## DAG模板

### create_simple_crawl_dag

创建简单爬取DAG。

```python
def create_simple_crawl_dag(
    dag_id: str,
    target_urls: List[str],
    crawler_config: Optional[Dict[str, Any]] = None,
    cleaner_config: Optional[Dict[str, Any]] = None,
    storage_config: Optional[Dict[str, Any]] = None,
    schedule_interval: Optional[str] = None,
    start_date: Optional[datetime] = None,
    **kwargs
) -> BaseDAG
```

### create_batch_process_dag

创建批量处理DAG。

```python
def create_batch_process_dag(
    dag_id: str,
    url_batches: List[List[str]],
    crawler_config: Optional[Dict[str, Any]] = None,
    cleaner_config: Optional[Dict[str, Any]] = None,
    storage_config: Optional[Dict[str, Any]] = None,
    schedule_interval: Optional[str] = None,
    start_date: Optional[datetime] = None,
    **kwargs
) -> BaseDAG
```

### create_realtime_dag

创建实时处理DAG。

```python
def create_realtime_dag(
    dag_id: str,
    data_source_config: Dict[str, Any],
    crawler_config: Optional[Dict[str, Any]] = None,
    cleaner_config: Optional[Dict[str, Any]] = None,
    storage_config: Optional[Dict[str, Any]] = None,
    sensor_config: Optional[Dict[str, Any]] = None,
    **kwargs
) -> BaseDAG
```

### create_scheduled_dag

创建定时调度DAG。

```python
def create_scheduled_dag(
    dag_id: str,
    schedule_time: str,  # "HH:MM"
    target_urls: List[str],
    crawler_config: Optional[Dict[str, Any]] = None,
    cleaner_config: Optional[Dict[str, Any]] = None,
    storage_config: Optional[Dict[str, Any]] = None,
    **kwargs
) -> BaseDAG
```

## XCom 数据交换

### XCom

任务间数据交换机制。

```python
class XCom:
    @classmethod
    def push(cls, key: str, value: Any, task_id: str, dag_id: str): 推送数据

    @classmethod
    def pull(cls, key: str, task_id: str, dag_id: str) -> Any: 拉取数据

    @classmethod
    def clear(cls, dag_id: str): 清理DAG的所有XCom数据
```

## 工具函数

### 获取实例

```python
# 获取调度器实例
from data_trans.workflow.scheduler import get_scheduler
scheduler = get_scheduler()

# 获取通知管理器实例
from data_trans.workflow.notifications import get_notification_manager
notification_manager = get_notification_manager(config)

# 获取配置管理器实例
from data_trans.workflow.config_manager import get_config_manager
config_manager = get_config_manager()

# 获取连接管理器实例
from data_trans.workflow.connections import get_connection_manager
connection_manager = get_connection_manager()

# 获取变量管理器实例
from data_trans.workflow.variables import get_variable_manager
variable_manager = get_variable_manager()
```

## 异常处理

所有异步方法都可能抛出异常，建议使用try-catch块进行处理：

```python
try:
    result = await task.execute(context)
except Exception as e:
    logger.error(f"任务执行失败: {e}")
    # 处理异常
```

## 类型提示

所有API都提供了完整的类型提示，支持IDE的自动补全和类型检查。

```python
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
```
