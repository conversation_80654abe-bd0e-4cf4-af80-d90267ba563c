"""
工作流管理模块 - Airflow兼容的工作流系统
由于Airflow在Windows上的兼容性问题，我们实现了一个简化的工作流管理系统
"""

from .base import BaseDAG, BaseOperator, BaseSensor
from .dag_templates import (
    create_batch_process_dag,
    create_realtime_dag,
    create_simple_crawl_dag,
)
from .operators import CleanerOperator, CrawlerOperator, StorageOperator
from .scheduler import WorkflowScheduler
from .sensors import DataSourceSensor, QueueSensor

__all__ = [
    "BaseOperator",
    "BaseDAG",
    "BaseSensor",
    "CrawlerOperator",
    "CleanerOperator",
    "StorageOperator",
    "DataSourceSensor",
    "QueueSensor",
    "WorkflowScheduler",
    "create_simple_crawl_dag",
    "create_batch_process_dag",
    "create_realtime_dag",
]
