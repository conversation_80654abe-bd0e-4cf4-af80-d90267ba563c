{"data_mtime": 1752408380, "dep_lines": [1, 2, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["email.mime.nonmultipart", "email.policy", "builtins", "_frozen_importlib", "abc", "email._policybase", "email.message", "email.mime.base", "types", "typing"], "hash": "0b17e6e8f63db3bf8b592edd1f47abe51c8a88c7", "id": "email.mime.text", "ignore_all": true, "interface_hash": "d441108d366d1178f6b143c87a6055703022ae88", "mtime": 1752386175, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\email\\mime\\text.pyi", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 302, "suppressed": [], "version_id": "1.16.1"}