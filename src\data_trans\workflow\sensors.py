"""
自定义传感器 - 监控数据源和队列状态
"""

import asyncio
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

try:
    from .base import BaseSensor
except ImportError:
    from base import BaseSensor

try:
    from ..config.settings import get_settings
    from ..queue.redis_queue import RedisQueue
except ImportError:
    # 如果无法导入，创建占位符
    class RedisQueue:
        def __init__(self, *args, **kwargs):
            pass

        async def get_queue_length(self, queue_name):
            return 0

    def get_settings():
        class MockSettings:
            class redis:
                host = "localhost"
                port = 6379
                db = 0
                password = None

        return MockSettings()


logger = logging.getLogger(__name__)


class DataSourceSensor(BaseSensor):
    """数据源传感器 - 监控数据源变化"""

    def __init__(
        self,
        task_id: str,
        dag,
        source_type: str,
        source_config: Dict[str, Any],
        check_interval: int = 60,
        **kwargs,
    ):
        super().__init__(task_id, dag, poke_interval=check_interval, **kwargs)
        self.source_type = source_type
        self.source_config = source_config
        self.last_check_time: Optional[datetime] = None

    async def poke(self, context: Dict[str, Any]) -> bool:
        """检查数据源是否有新数据"""
        task_instance = context["task_instance"]

        try:
            if self.source_type == "file":
                return await self._check_file_source(task_instance)
            elif self.source_type == "database":
                return await self._check_database_source(task_instance)
            elif self.source_type == "api":
                return await self._check_api_source(task_instance)
            else:
                task_instance.log_info(f"不支持的数据源类型: {self.source_type}")
                return False

        except Exception as e:
            task_instance.log_info(f"检查数据源时出错: {str(e)}")
            return False

    async def _check_file_source(self, task_instance) -> bool:
        """检查文件数据源"""
        file_path = self.source_config.get("path")
        if not file_path:
            task_instance.log_info("文件路径未配置")
            return False

        path = Path(file_path)
        if not path.exists():
            task_instance.log_info(f"文件不存在: {file_path}")
            return False

        # 检查文件修改时间
        file_mtime = datetime.fromtimestamp(path.stat().st_mtime)

        if self.last_check_time is None:
            self.last_check_time = datetime.now()
            task_instance.log_info(f"初始化检查时间: {self.last_check_time}")
            return False

        if file_mtime > self.last_check_time:
            task_instance.log_info(
                f"检测到文件更新: {file_path}, 修改时间: {file_mtime}"
            )
            return True

        task_instance.log_info(f"文件无更新: {file_path}")
        return False

    async def _check_database_source(self, task_instance) -> bool:
        """检查数据库数据源"""
        # 这里简化实现，实际应该连接数据库检查
        table_name = self.source_config.get("table")
        check_column = self.source_config.get("check_column", "updated_at")

        task_instance.log_info(f"检查数据库表: {table_name}, 列: {check_column}")

        # 模拟数据库检查
        # 实际实现应该执行SQL查询检查是否有新记录
        import random

        has_new_data = random.choice([True, False])

        if has_new_data:
            task_instance.log_info(f"检测到数据库新数据: {table_name}")
        else:
            task_instance.log_info(f"数据库无新数据: {table_name}")

        return has_new_data

    async def _check_api_source(self, task_instance) -> bool:
        """检查API数据源"""
        api_url = self.source_config.get("url")
        check_field = self.source_config.get("check_field", "last_updated")

        task_instance.log_info(f"检查API数据源: {api_url}")

        try:
            import httpx

            async with httpx.AsyncClient() as client:
                response = await client.get(api_url)
                response.raise_for_status()

                data = response.json()
                current_value = data.get(check_field)

                if current_value is None:
                    task_instance.log_info(f"API响应中未找到检查字段: {check_field}")
                    return False

                # 比较值是否变化
                last_value = getattr(self, "_last_api_value", None)
                if last_value is None:
                    self._last_api_value = current_value
                    task_instance.log_info(f"初始化API检查值: {current_value}")
                    return False

                if current_value != last_value:
                    task_instance.log_info(
                        f"检测到API数据更新: {last_value} -> {current_value}"
                    )
                    self._last_api_value = current_value
                    return True

                task_instance.log_info(f"API数据无更新: {current_value}")
                return False

        except Exception as e:
            task_instance.log_info(f"检查API数据源失败: {str(e)}")
            return False


class QueueSensor(BaseSensor):
    """队列传感器 - 监控Redis队列状态"""

    def __init__(
        self,
        task_id: str,
        dag,
        queue_name: str,
        min_messages: int = 1,
        max_messages: Optional[int] = None,
        check_interval: int = 30,
        **kwargs,
    ):
        super().__init__(task_id, dag, poke_interval=check_interval, **kwargs)
        self.queue_name = queue_name
        self.min_messages = min_messages
        self.max_messages = max_messages
        self._queue: Optional[RedisQueue] = None

    async def _get_queue(self) -> RedisQueue:
        """获取队列实例"""
        if self._queue is None:
            settings = get_settings()
            self._queue = RedisQueue(
                host=settings.redis.host,
                port=settings.redis.port,
                db=settings.redis.db,
                password=settings.redis.password,
            )
        return self._queue

    async def poke(self, context: Dict[str, Any]) -> bool:
        """检查队列状态"""
        task_instance = context["task_instance"]

        try:
            queue = await self._get_queue()

            # 获取队列长度
            queue_length = await queue.get_queue_length(self.queue_name)
            task_instance.log_info(f"队列 {self.queue_name} 当前长度: {queue_length}")

            # 检查最小消息数
            if queue_length < self.min_messages:
                task_instance.log_info(
                    f"队列消息数不足，需要至少 {self.min_messages} 条"
                )
                return False

            # 检查最大消息数（如果设置）
            if self.max_messages and queue_length > self.max_messages:
                task_instance.log_info(
                    f"队列消息数过多，超过最大限制 {self.max_messages} 条"
                )
                return False

            task_instance.log_info(f"队列状态满足条件，消息数: {queue_length}")
            return True

        except Exception as e:
            task_instance.log_info(f"检查队列状态时出错: {str(e)}")
            return False


class TimeSensor(BaseSensor):
    """时间传感器 - 基于时间条件触发"""

    def __init__(
        self,
        task_id: str,
        dag,
        target_time: str,  # 格式: "HH:MM" 或 "YYYY-MM-DD HH:MM:SS"
        check_interval: int = 60,
        **kwargs,
    ):
        super().__init__(task_id, dag, poke_interval=check_interval, **kwargs)
        self.target_time = target_time
        self._target_datetime: Optional[datetime] = None

    def _parse_target_time(self) -> datetime:
        """解析目标时间"""
        if self._target_datetime is not None:
            return self._target_datetime

        try:
            # 尝试解析完整日期时间
            if len(self.target_time) > 5:
                self._target_datetime = datetime.strptime(
                    self.target_time, "%Y-%m-%d %H:%M:%S"
                )
            else:
                # 解析时间，使用今天的日期
                time_part = datetime.strptime(self.target_time, "%H:%M").time()
                today = datetime.now().date()
                self._target_datetime = datetime.combine(today, time_part)

                # 如果时间已过，设置为明天
                if self._target_datetime <= datetime.now():
                    from datetime import timedelta

                    self._target_datetime += timedelta(days=1)

            return self._target_datetime

        except ValueError as e:
            raise ValueError(f"无效的时间格式: {self.target_time}, 错误: {e}")

    async def poke(self, context: Dict[str, Any]) -> bool:
        """检查是否到达目标时间"""
        task_instance = context["task_instance"]

        try:
            target_dt = self._parse_target_time()
            current_dt = datetime.now()

            task_instance.log_info(f"当前时间: {current_dt}, 目标时间: {target_dt}")

            if current_dt >= target_dt:
                task_instance.log_info("已到达目标时间")
                return True

            remaining = target_dt - current_dt
            task_instance.log_info(f"距离目标时间还有: {remaining}")
            return False

        except Exception as e:
            task_instance.log_info(f"检查时间条件时出错: {str(e)}")
            return False


class HttpSensor(BaseSensor):
    """HTTP传感器 - 监控HTTP端点状态"""

    def __init__(
        self,
        task_id: str,
        dag,
        endpoint: str,
        method: str = "GET",
        expected_status: int = 200,
        headers: Optional[Dict[str, str]] = None,
        check_interval: int = 60,
        **kwargs,
    ):
        super().__init__(task_id, dag, poke_interval=check_interval, **kwargs)
        self.endpoint = endpoint
        self.method = method.upper()
        self.expected_status = expected_status
        self.headers = headers or {}

    async def poke(self, context: Dict[str, Any]) -> bool:
        """检查HTTP端点状态"""
        task_instance = context["task_instance"]

        try:
            import httpx

            task_instance.log_info(f"检查HTTP端点: {self.method} {self.endpoint}")

            async with httpx.AsyncClient() as client:
                response = await client.request(
                    method=self.method,
                    url=self.endpoint,
                    headers=self.headers,
                    timeout=30.0,
                )

                task_instance.log_info(f"HTTP响应状态: {response.status_code}")

                if response.status_code == self.expected_status:
                    task_instance.log_info("HTTP端点状态正常")
                    return True
                else:
                    task_instance.log_info(
                        f"HTTP端点状态异常，期望: {self.expected_status}, 实际: {response.status_code}"
                    )
                    return False

        except Exception as e:
            task_instance.log_info(f"检查HTTP端点时出错: {str(e)}")
            return False
