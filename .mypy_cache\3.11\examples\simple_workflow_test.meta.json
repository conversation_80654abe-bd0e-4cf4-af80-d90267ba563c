{"data_mtime": 1752408380, "dep_lines": [5, 6, 7, 8, 9, 319, 1, 1, 1, 1, 1, 26, 74, 109, 147, 192, 193, 239], "dep_prios": [10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 20, 20, 20, 20, 20, 20, 20], "dependencies": ["asyncio", "logging", "os", "sys", "datetime", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "a898c3dd71c55e78e3f1a8f377c2d39c8883852a", "id": "examples.simple_workflow_test", "ignore_all": false, "interface_hash": "34de10f78e4e32bde19fa8d90d5613b3e2f2bb38", "mtime": 1752408378, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "examples\\simple_workflow_test.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 9576, "suppressed": ["data_trans.workflow.base", "data_trans.workflow.sensors", "data_trans.workflow.notifications", "data_trans.workflow.scheduler", "data_trans.workflow.config_manager", "data_trans.workflow.connections", "data_trans.workflow.dag_templates"], "version_id": "1.16.1"}