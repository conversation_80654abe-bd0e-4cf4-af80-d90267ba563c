{".class": "MypyFile", "_fullname": "src.data_trans.workflow", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseDAG": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.base.BaseDAG", "kind": "Gdef"}, "BaseOperator": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.base.BaseOperator", "kind": "Gdef"}, "BaseSensor": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.base.BaseSensor", "kind": "Gdef"}, "CleanerOperator": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.operators.CleanerOperator", "kind": "Gdef"}, "CrawlerOperator": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.operators.CrawlerOperator", "kind": "Gdef"}, "DataSourceSensor": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.sensors.DataSourceSensor", "kind": "Gdef"}, "QueueSensor": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.sensors.QueueSensor", "kind": "Gdef"}, "StorageOperator": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.operators.StorageOperator", "kind": "Gdef"}, "WorkflowScheduler": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.scheduler.WorkflowScheduler", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.data_trans.workflow.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.workflow.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.workflow.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.workflow.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.workflow.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.workflow.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.workflow.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.workflow.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "create_batch_process_dag": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.dag_templates.create_batch_process_dag", "kind": "Gdef"}, "create_realtime_dag": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.dag_templates.create_realtime_dag", "kind": "Gdef"}, "create_simple_crawl_dag": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.workflow.dag_templates.create_simple_crawl_dag", "kind": "Gdef"}}, "path": "src\\data_trans\\workflow\\__init__.py"}