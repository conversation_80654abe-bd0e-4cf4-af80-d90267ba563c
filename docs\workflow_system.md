# DataTrans 工作流系统文档

## 概述

DataTrans 工作流系统是一个类似 Apache Airflow 的工作流管理平台，专门为数据采集、清洗和存储任务设计。由于 Airflow 在 Windows 环境下的兼容性问题，我们开发了这个轻量级但功能完整的工作流系统。

## 核心特性

- **DAG（有向无环图）管理**：支持复杂的任务依赖关系
- **多种操作器**：爬虫、清洗、存储等专用操作器
- **传感器系统**：监控数据源、队列、时间等条件
- **调度器**：支持定时和手动触发
- **通知系统**：多渠道通知支持（日志、邮件、Slack、钉钉等）
- **配置管理**：连接和变量的统一管理
- **实时监控**：任务执行状态和日志跟踪

## 系统架构

```
工作流系统
├── 核心组件 (workflow/base.py)
│   ├── BaseDAG - DAG基础类
│   ├── BaseOperator - 操作器基础类
│   ├── BaseSensor - 传感器基础类
│   └── XCom - 数据交换机制
├── 操作器 (workflow/operators.py)
│   ├── CrawlerOperator - 爬虫操作器
│   ├── CleanerOperator - 清洗操作器
│   ├── StorageOperator - 存储操作器
│   └── NotificationOperator - 通知操作器
├── 传感器 (workflow/sensors.py)
│   ├── DataSourceSensor - 数据源监控
│   ├── QueueSensor - 队列监控
│   ├── TimeSensor - 时间触发
│   └── HttpSensor - HTTP端点监控
├── 调度器 (workflow/scheduler.py)
│   └── WorkflowScheduler - 工作流调度管理
├── 通知系统 (workflow/notifications.py)
│   ├── LogNotifier - 日志通知
│   ├── EmailNotifier - 邮件通知
│   ├── SlackNotifier - Slack通知
│   ├── DingTalkNotifier - 钉钉通知
│   └── WebhookNotifier - Webhook通知
├── 配置管理 (workflow/config_manager.py)
│   ├── ConnectionManager - 连接管理
│   ├── VariableManager - 变量管理
│   └── ConfigManager - 统一配置管理
└── DAG模板 (workflow/dag_templates.py)
    ├── create_simple_crawl_dag - 简单爬取模板
    ├── create_batch_process_dag - 批量处理模板
    ├── create_realtime_dag - 实时处理模板
    └── create_scheduled_dag - 定时调度模板
```

## 快速开始

### 1. 基本使用

```python
import asyncio
from datetime import datetime
from data_trans.workflow.base import BaseDAG, BaseOperator
from data_trans.workflow.scheduler import get_scheduler

# 创建自定义操作器
class PrintOperator(BaseOperator):
    def __init__(self, task_id: str, dag, message: str, **kwargs):
        super().__init__(task_id, dag, **kwargs)
        self.message = message

    async def execute(self, context):
        print(f"[{self.task_id}] {self.message}")
        return f"完成: {self.message}"

# 创建DAG
dag = BaseDAG(
    dag_id="hello_world",
    description="Hello World 工作流",
    start_date=datetime.now()
)

# 创建任务
task1 = PrintOperator("task1", dag, "Hello")
task2 = PrintOperator("task2", dag, "World")

# 设置依赖关系
task1 >> task2

# 执行DAG
async def main():
    scheduler = get_scheduler()
    scheduler.register_dag(dag)
    run_id = await scheduler.trigger_dag("hello_world")
    print(f"DAG已启动，运行ID: {run_id}")

asyncio.run(main())
```

### 2. 使用DAG模板

```python
from data_trans.workflow.dag_templates import create_simple_crawl_dag

# 创建简单爬取DAG
dag = create_simple_crawl_dag(
    dag_id="my_crawl_job",
    target_urls=["https://example.com"],
    crawler_config={"timeout": 30},
    cleaner_config={"remove_html": True},
    storage_config={"type": "mongodb", "collection": "crawl_data"}
)
```

## 核心概念

### DAG（有向无环图）

DAG 是工作流的核心概念，定义了任务之间的依赖关系：

```python
from data_trans.workflow.base import BaseDAG

dag = BaseDAG(
    dag_id="example_dag",
    description="示例DAG",
    schedule_interval="@daily",  # 每天执行
    start_date=datetime(2024, 1, 1),
    catchup=False,  # 不执行历史任务
    max_active_runs=1  # 最大并行运行数
)
```

### 操作器（Operators）

操作器定义了具体的任务逻辑：

#### 爬虫操作器
```python
from data_trans.workflow.operators import CrawlerOperator

crawler_task = CrawlerOperator(
    task_id="crawl_data",
    dag=dag,
    crawler_config={
        "timeout": 30,
        "max_retries": 3,
        "user_agent": "MyBot/1.0"
    },
    target_urls=["https://example.com"],
    output_key="crawl_result"
)
```

#### 清洗操作器
```python
from data_trans.workflow.operators import CleanerOperator

cleaner_task = CleanerOperator(
    task_id="clean_data",
    dag=dag,
    cleaner_config={
        "remove_html": True,
        "normalize_whitespace": True
    },
    input_key="crawl_result",
    output_key="clean_result"
)
```

#### 存储操作器
```python
from data_trans.workflow.operators import StorageOperator

storage_task = StorageOperator(
    task_id="store_data",
    dag=dag,
    storage_config={
        "type": "mongodb",
        "collection": "my_data"
    },
    input_key="clean_result"
)
```

### 传感器（Sensors）

传感器用于监控外部条件：

#### 时间传感器
```python
from data_trans.workflow.sensors import TimeSensor

time_sensor = TimeSensor(
    task_id="wait_for_time",
    dag=dag,
    target_time="09:00",  # 每天9点触发
    check_interval=60
)
```

#### 数据源传感器
```python
from data_trans.workflow.sensors import DataSourceSensor

file_sensor = DataSourceSensor(
    task_id="wait_for_file",
    dag=dag,
    source_type="file",
    source_config={"path": "/data/input.csv"},
    check_interval=30
)
```

### XCom 数据交换

XCom 用于任务间的数据传递：

```python
# 在任务中推送数据
XCom.push("my_key", "my_value", task_id, dag_id)

# 在下游任务中拉取数据
value = XCom.pull("my_key", upstream_task_id, dag_id)
```

## 配置管理

### 连接管理

```python
from data_trans.workflow.config_manager import get_config_manager

config_manager = get_config_manager()

# 添加数据库连接
config_manager.add_connection(
    conn_id="my_postgres",
    conn_type="postgres",
    host="localhost",
    port=5432,
    schema="mydb",
    login="user",
    password="password"
)

# 获取连接
conn = config_manager.get_connection("my_postgres")
uri = conn.get_uri()
```

### 变量管理

```python
# 设置变量
config_manager.set_variable(
    "api_key",
    "secret_key_value",
    description="API密钥",
    is_encrypted=True
)

# 获取变量
api_key = config_manager.get_variable("api_key")
```

## 通知系统

### 配置通知

```python
from data_trans.workflow.notifications import get_notification_manager

notification_config = {
    'log': {'enabled': True},
    'email': {
        'enabled': True,
        'smtp_host': 'smtp.gmail.com',
        'smtp_port': 587,
        'smtp_user': '<EMAIL>',
        'smtp_password': 'your_password',
        'recipients': ['<EMAIL>']
    },
    'slack': {
        'enabled': True,
        'webhook_url': 'https://hooks.slack.com/...'
    }
}

manager = get_notification_manager(notification_config)
```

### 发送通知

```python
# 发送自定义通知
await manager.send_notification(
    message="任务执行完成",
    subject="工作流通知",
    channels=['log', 'email']
)

# 发送成功通知
await manager.send_success_notification(
    dag_id="my_dag",
    execution_date="2024-01-01T10:00:00"
)
```

## 调度器使用

### 启动调度器

```python
from data_trans.workflow.scheduler import get_scheduler

scheduler = get_scheduler()

# 注册DAG
scheduler.register_dag(dag)

# 手动触发
run_id = await scheduler.trigger_dag("my_dag")

# 启动定时调度
await scheduler.start_scheduler(check_interval=60)
```

### 监控执行状态

```python
# 获取DAG运行状态
dag_run = scheduler.get_dag_run(run_id)
print(f"状态: {dag_run.state}")
print(f"日志: {dag_run.log_messages}")

# 获取调度器状态
status = scheduler.get_scheduler_status()
print(f"调度器状态: {status}")
```

## 最佳实践

### 1. DAG设计原则

- **单一职责**：每个DAG应该有明确的业务目标
- **幂等性**：任务应该可以安全地重复执行
- **错误处理**：合理设置重试次数和错误处理逻辑
- **资源管理**：避免创建过多并发任务

### 2. 任务依赖管理

```python
# 线性依赖
task1 >> task2 >> task3

# 扇出模式
task1 >> [task2, task3, task4]

# 扇入模式
[task1, task2, task3] >> task4

# 复杂依赖
task1 >> task2
task1 >> task3
[task2, task3] >> task4
```

### 3. 错误处理

```python
# 设置重试
task = MyOperator(
    task_id="my_task",
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=5)
)

# 错误回调
def on_failure_callback(context):
    print(f"任务失败: {context['task_instance'].task_id}")

dag = BaseDAG(
    dag_id="my_dag",
    on_failure_callback=on_failure_callback
)
```

### 4. 性能优化

- **并行执行**：合理设计任务依赖，最大化并行度
- **资源限制**：设置合理的并发限制
- **数据传递**：避免在XCom中传递大量数据
- **日志管理**：定期清理旧的执行日志

## 故障排除

### 常见问题

1. **任务卡住不执行**
   - 检查任务依赖关系
   - 确认上游任务已完成
   - 查看调度器状态

2. **内存使用过高**
   - 减少并发任务数量
   - 优化数据处理逻辑
   - 清理XCom数据

3. **通知发送失败**
   - 检查网络连接
   - 验证配置信息
   - 查看错误日志

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 单独测试任务
result = await task.execute(context)

# 检查XCom数据
data = XCom.pull("key", "task_id", "dag_id")
```

## 部署指南

### 开发环境

1. 安装依赖：
```bash
uv add apache-airflow
```

2. 运行测试：
```bash
uv run python examples/minimal_workflow_test.py
```

### 生产环境

1. **配置文件管理**
   - 使用环境变量管理敏感信息
   - 分离开发和生产配置

2. **监控和告警**
   - 配置多渠道通知
   - 设置关键指标监控

3. **备份和恢复**
   - 定期备份配置文件
   - 建立灾难恢复流程

4. **安全考虑**
   - 加密敏感变量
   - 限制网络访问
   - 定期更新依赖

## 扩展开发

### 自定义操作器

```python
from data_trans.workflow.base import BaseOperator

class CustomOperator(BaseOperator):
    def __init__(self, task_id: str, dag, custom_param: str, **kwargs):
        super().__init__(task_id, dag, **kwargs)
        self.custom_param = custom_param

    async def execute(self, context):
        # 实现自定义逻辑
        result = self.do_custom_work()
        return result

    def do_custom_work(self):
        # 自定义工作逻辑
        pass
```

### 自定义传感器

```python
from data_trans.workflow.base import BaseSensor

class CustomSensor(BaseSensor):
    async def poke(self, context):
        # 实现检查逻辑
        condition_met = self.check_condition()
        return condition_met

    def check_condition(self):
        # 检查条件是否满足
        return True
```

## 总结

DataTrans 工作流系统提供了完整的工作流管理能力，支持复杂的数据处理任务编排。通过合理使用DAG、操作器、传感器等组件，可以构建高效、可靠的数据处理流水线。

更多详细信息请参考源代码和示例文件。
